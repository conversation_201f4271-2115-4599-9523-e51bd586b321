<!--
 * @Author: 智慧园区开发团队
 * @Date: 2025-01-01
 * @Description: 告警分布统计组件 - 显示雷达图
-->
<template>
    <!-- 使用Panel组件包裹 -->
    <Panel size="big" class="afcx" title="告警分布统计">
        <div class="alarm-distribution-container">
            <!-- 雷达图区域 -->
            <div class="radar-chart">
                <svg width="100%" height="100%" viewBox="0 0 300 300" preserveAspectRatio="xMidYMid meet">
                    <!-- 雷达图背景网格 -->
                    <g class="radar-grid">
                        <!-- 绘制5层八边形网格 -->
                        <polygon v-for="level in 5" :key="level"
                            :points="getOctagonPoints(150, 150, level * 20)"
                            fill="none"
                            stroke="rgba(255, 255, 255, 0.1)"
                            stroke-width="1"
                        />
                    </g>
                    
                    <!-- 雷达图轴线 -->
                    <g class="radar-axis">
                        <line v-for="(label, index) in radarLabels" :key="index"
                            x1="150" y1="150"
                            :x2="getAxisEndPoint(index).x"
                            :y2="getAxisEndPoint(index).y"
                            stroke="rgba(255, 255, 255, 0.15)"
                            stroke-width="1"
                        />
                    </g>
                    
                    <!-- 数据区域填充 -->
                    <polygon
                        :points="getDataPoints()"
                        fill="rgba(0, 191, 255, 0.25)"
                        stroke="#00bfff"
                        stroke-width="2"
                    />
                    
                    <!-- 数据点 -->
                    <circle v-for="(point, index) in getDataPointsArray()" :key="index"
                        :cx="point.x"
                        :cy="point.y"
                        r="3"
                        fill="#00bfff"
                        stroke="#ffffff"
                        stroke-width="1"
                    />
                </svg>
                
                <!-- 标签文字 - 使用绝对定位确保响应式 -->
                <div class="radar-labels">
                    <div v-for="(label, index) in radarLabels" :key="index"
                        class="radar-label"
                        :class="`label-${index}`"
                    >
                        {{ label }}
                    </div>
                </div>
            </div>
        </div>
    </Panel>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import { getWeather } from '@/apis';
import { useStore } from "vuex";

// 雷达图标签 - 根据图片更新为8个标签
const radarLabels = ref(['第1栋', '地下室', '园区室外', '第2栋', '第3栋', '第4栋', '第5栋', '第6栋']);

const radarData = ref([85, 70, 95, 75, 80, 65, 90, 88]);

const store = useStore();

watch(
    () => store.state.updateTime.active,
    (newVal) => {
        getApi();
    },
)

onMounted(() => {
    getApi();
})

const getApi = async () => {
    try {
        const res = await getWeather();
        // 后续接口出来后直接替换
        // radarData.value = res.alarmDistribution || radarData.value;
        // radarLabels.value = res.alarmLabels || radarLabels.value;
    } catch (error) {
        console.log('API调用失败:', error);
    }
}

// 获取八边形顶点坐标
const getOctagonPoints = (cx, cy, radius) => {
    const points = [];
    for (let i = 0; i < 8; i++) {
        const angle = (Math.PI / 4) * i - Math.PI / 2;
        const x = cx + radius * Math.cos(angle);
        const y = cy + radius * Math.sin(angle);
        points.push(`${x},${y}`);
    }
    return points.join(' ');
};

// 获取轴线终点坐标
const getAxisEndPoint = (index) => {
    const angle = (Math.PI / 4) * index - Math.PI / 2;
    const x = 150 + 100 * Math.cos(angle);
    const y = 150 + 100 * Math.sin(angle);
    return { x, y };
};

// 获取数据点坐标
const getDataPoints = () => {
    const points = [];
    radarData.value.forEach((value, index) => {
        const angle = (Math.PI / 4) * index - Math.PI / 2;
        const radius = (value / 100) * 100;
        const x = 150 + radius * Math.cos(angle);
        const y = 150 + radius * Math.sin(angle);
        points.push(`${x},${y}`);
    });
    return points.join(' ');
};

// 获取数据点数组
const getDataPointsArray = () => {
    return radarData.value.map((value, index) => {
        const angle = (Math.PI / 4) * index - Math.PI / 2;
        const radius = (value / 100) * 100;
        const x = 150 + radius * Math.cos(angle);
        const y = 150 + radius * Math.sin(angle);
        return { x, y };
    });
};
</script>

<style lang="scss" scoped>

.afcx {
    width: 100%;  
    max-width: 490px;  
    min-width: 350px;  
    height: 285px; 
  
    
    .alarm-distribution-container {
        padding: 20px;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        
        /* 雷达图样式 */
        .radar-chart {
            position: relative;
            width: 280px;
            height: 280px;
           
            svg {
                width: 100%;
                height: 100%;
            }
          
            .radar-labels {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                pointer-events: none;
                
                .radar-label {
                    position: absolute;
                    color: rgba(255, 255, 255, 0.9);
                    font-size: 13px;
                    font-weight: 500;
                    white-space: nowrap;
                    text-shadow: 0 0 6px rgba(0, 0, 0, 0.8);
                    z-index: 10;
                    
                    
                    &.label-0 { /* 第一栋 - 正上方 */
                        top: 8%;
                        left: 50%;
                        transform: translateX(-50%);
                    }
                    
                    &.label-1 { /* 地下室 - 右上方 */
                        top: 18%;
                        right: 8%;
                        transform: translateX(0);
                    }
                    
                    &.label-2 { /* 园区室外 - 正右方 */
                        top: 50%;
                        right: 2%;
                        transform: translateY(-50%);
                    }
                    
                    &.label-3 { /* 第二栋 - 右下方 */
                        bottom: 18%;
                        right: 8%;
                        transform: translateX(0);
                    }
                    
                    &.label-4 { /* 第三栋 - 正下方 */
                        bottom: 8%;
                        left: 50%;
                        transform: translateX(-50%);
                    }
                    
                    &.label-5 { /* 第四栋 - 左下方 */
                        bottom: 18%;
                        left: 8%;
                        transform: translateX(0);
                    }
                    
                    &.label-6 { /* 第五栋 - 正左方 */
                        top: 50%;
                        left: 2%;
                        transform: translateY(-50%);
                    }
                    
                    &.label-7 { /* 第六栋 - 左上方 */
                        top: 18%;
                        left: 8%;
                        transform: translateX(0);
                    }
                }
            }
        }
    }
}
</style> 