<!--
 * @Author: 智慧园区开发团队
 * @Date: 2025-01-01
 * @Description: 园区当日告警数据组件 - 美观的表格设计
-->
<template>
    <!-- 使用Panel组件包裹 -->
    <Panel size="medium" class="afrz" title="指标异常设备排名">
        <div class="record-container">
        
            <div class="custom-table">
                <!-- 表头 -->
                <div class="table-header">
                    <div class="header-cell id-col">排名</div>
                    <div class="header-cell time-col"></div>
                    <div class="header-cell content-col"></div>
                    <div class="header-cell status-col">告警次数</div>
                </div>
                
                <!-- 表体 -->
                <div class="table-body">
                    <div 
                        v-for="(item, index) in alarmList" 
                        :key="index"
                        class="table-row"
                        :class="{ 'row-even': index % 2 === 0 }"
                    >
                        <div class="body-cell id-col">{{ item.id }}</div>
                        <div class="body-cell time-col"></div>
                        <div class="body-cell content-col"></div>
                        <div class="body-cell status-col">
                            <span class="status-badge" >
                                {{ item.status }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </Panel>
</template>

<script setup>
import { reactive, ref, onMounted, watch } from "vue";
import { topRanking } from '@/apis';
import { useStore } from "vuex";

// 告警数据列表
const alarmList = ref([
    { id: 1, time: '2025-04-16 13:37:52', content: '3#5F厅行石柜', status: 66, statusClass: 'status-alarm' },
    { id: 2, time: '2025-04-16 13:37:52', content: '3#5F厅行石柜', status: 99, statusClass: 'status-normal' },
    { id: 3, time: '2025-04-16 13:53:22', content: '三期5#-1F消防设备', status: 99, statusClass: 'status-normal' },
    { id: 4, time: '2025-04-16 13:53:22', content: '三期5#-1F消防设备', status: 66, statusClass: 'status-alarm' },
    { id: 5, time: '2025-04-16 14:15:30', content: '北区电力监控', status: 99, statusClass: 'status-normal' },
    { id: 6, time: '2025-04-16 14:25:45', content: '南区安防系统', status: 66, statusClass: 'status-alarm' },
    { id: 7, time: '2025-04-16 14:40:12', content: '东门闸机设备', status: 99, statusClass: 'status-normal' },
    { id: 8, time: '2025-04-16 15:10:33', content: '西区温度传感器', status: 66, statusClass: 'status-alarm' }
]);

const store = useStore();

const getTopRanking = async () => {
    const res = await topRanking();
    console.log(res);
}

watch(
    () => store.state.updateTime.active,
    (newVal) => {
        getTopRanking();
    },
)

onMounted(() => {
    getTopRanking();
})


</script>

<style lang="scss" scoped>

.afrz {
    width: 100%;
    height: 380px;
    // background: red;

    .record-container {
        padding: 10px;
        height: calc(100%);
        
        overflow: hidden;
        
        /* 自定义表格样式 */
        .custom-table {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
        
            
            /* 表头样式 */
            .table-header {
                display: flex;
        
                
                .header-cell {
                    padding: 10px 4px;
                    color: rgba(255, 255, 255, 0.9);
                    font-size: 14px;
                    text-align: center;
                    
                    &.id-col {
                        width: 18%;
                        min-width: 60px;
                    }
                    
                    &.time-col {
                        width: 35%;
                        min-width: 140px;
                    }
                    
                    &.content-col {
                        width: 32%;
                        min-width: 120px;
                    }
                    
                    &.status-col {
                        width: 15%;
                        min-width: 60px;
                    }
                }
            }
            
            /* 表体样式 */
            .table-body {
                flex: 1;
                overflow-x: auto;
       
                
                /* 隐藏滚动条但保持滚动功能 */
                &::-webkit-scrollbar {
                    display: none;
                }
                scrollbar-width: none;
                -ms-overflow-style: none;
                
                .table-row {
                  display: flex;
                    &:last-child {
                        border-bottom: none;
                    }
     
                    

                    
                    .body-cell {
                        padding: 10px 10px;
                        color: rgba(255, 255, 255, 0.8);
                        font-size: 13px;
                        line-height: 1.4;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        text-align: center;
                        
                        &.id-col {
                            width: 18%;
                            min-width: 60px;
                            font-weight: 500;
                            color: rgba(255, 255, 255, 0.9);
                        }
                        
                        &.time-col {
                            width: 35%;
                            min-width: 140px;
                            color: rgba(255, 255, 255, 0.7);
                            font-family: 'Courier New', monospace;
                        }
                        
                        &.content-col {
                            width: 32%;
                            min-width: 120px;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        }
                        
                        &.status-col {
                            width: 15%;
                            min-width: 60px;
                        }
                    }
                }
            }
        }
        

    }

}
</style> 