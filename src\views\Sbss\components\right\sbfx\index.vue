<template>
    <div>
        <Panel size="big" class="sbtj-container" title="告警明细">
            <div class="record-container">
                <!-- 自定义美观表格 -->
                <div class="custom-table">
                    <!-- 表头 -->
                    <div class="table-header">
                        <div class="header-cell index-col">序号</div>
                        <div class="header-cell type-col">告警类型</div>
                        <div class="header-cell time-col">告警时间</div>
                        <div class="header-cell content-col">告警内容</div>
                    </div>
                    
                    <!-- 表体 -->
                    <div class="table-body">
                        <div 
                            v-for="(item, index) in data.alarmList" 
                            :key="index"
                            class="table-row"
                            :class="{ 'row-even': index % 2 === 0 }"
                        >
                            <div class="body-cell index-col">{{ index + 1 }}</div>
                            <div class="body-cell type-col">{{ item.alarmType }}</div>
                            <div class="body-cell time-col">{{ item.alarmTime }}</div>
                            <div class="body-cell content-col">{{ item.alarmContent }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </Panel>
    </div>
</template>

<script setup>
import { reactive, onMounted, watch } from 'vue'
import { warnList } from '@/apis'
import { useStore } from 'vuex'

const store = useStore()

// 数据定义 - 按照图片内容设置
const data = reactive({
    alarmList: [
        {
            alarmType: '综合安防',
            alarmTime: '2025-04-18 17:37:52',
            alarmContent: '1F大堂照明'
        },
        {
            alarmType: '环境监测',
            alarmTime: '2025-04-19 13:37:52',
            alarmContent: '1F检测中心照明'
        },
        {
            alarmType: '设备设施',
            alarmTime: '2025-04-20 13:37:52',
            alarmContent: '3F宴会厅照明'
        },
        {
            alarmType: '设备设施',
            alarmTime: '2025-04-21 13:37:52',
            alarmContent: '8F右中庭照明'
        },
        {
            alarmType: '综合安防',
            alarmTime: '2025-04-21 13:37:52',
            alarmContent: '8F右中庭照明'
        },
        {
            alarmType: '环境监测',
            alarmTime: '2025-04-21 13:37:52',
            alarmContent: '1F中庭照明'
        },
        {
            alarmType: '环境监测',
            alarmTime: '2025-04-21 13:37:52',
            alarmContent: '1F中庭照明'
        },
        {
            alarmType: '环境监测',
            alarmTime: '2025-04-21 13:37:52',
            alarmContent: '1F中庭照明'
        }
    ]
})

const getWarnList = async () => {
    try {
        // 传递scene参数，值为environment（环境空间）
        const res = await warnList({ scene: 'device' });
        console.log('环境告警统计数据:', res);
        
   
        if (res) {
      
        }
    } catch (error) {
        console.error('获取环境告警统计失败:', error);
    }
};

// 监听数据更新
watch(
    () => store.state.updateTime.active,
    (newVal) => {
        getWarnList()
    }
)

onMounted(() => {
    getWarnList()
})
</script>
 
<style lang="scss" scoped>
.sbtj-container {
    width: 100%;
    height: 380px;
    
    /* 记录容器样式 */
    .record-container {
        padding: 15px;
        height: calc(100%);
        overflow: hidden;
        
        /* 自定义表格样式 */
        .custom-table {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            
            /* 表头样式 */
            .table-header {
                display: flex;
                
                .header-cell {
                    padding: 10px 4px;
                    // color: rgba(255, 255, 255, 0.9);
                    font-size: 14px;
                    text-align: center;
                    
                    &.index-col {
                        width: 15%;
                        min-width: 50px;
                    }
                    
                    &.type-col {
                        width: 25%;
                        min-width: 100px;
                    }
                    
                    &.time-col {
                        width: 35%;
                        min-width: 140px;
                    }
                    
                    &.content-col {
                        width: 25%;
                        min-width: 100px;
                    }
                }
            }
            
            /* 表体样式 */
            .table-body {
                flex: 1;
                overflow-y: auto;
                border-radius: 0 0 6px 6px;
                margin-top: 10px;
             
                &::-webkit-scrollbar {
                    display: none;
                }
                scrollbar-width: none;
                -ms-overflow-style: none;
                
                .table-row {
                    display: flex;
                    transition: all 0.2s ease;
                    
                    &:last-child {
                        border-bottom: none;
                    }
                    
                    .body-cell {
                        padding: 10px 10px;
                        color: rgba(255, 255, 255, 0.8);
                        font-size: 13px;
                        line-height: 1.4;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        text-align: center;
                        
                        &.index-col {
                            width: 15%;
                            min-width: 50px;
                            font-weight: 500;
                            color: rgba(255, 255, 255, 0.9);
                        }
                        
                        &.type-col {
                            width: 25%;
                            min-width: 100px;
                            color: rgba(255, 255, 255, 0.9);
                        }
                        
                        &.time-col {
                            width: 35%;
                            min-width: 140px;
                            color: #fff;
                            font-family: 'Courier New', monospace;
                            white-space: nowrap; /* 时间在一行显示 */
                        }
                        
                        &.content-col {
                            width: 25%;
                            min-width: 100px;
                            overflow: hidden; /* 隐藏超出内容 */
                            text-overflow: ellipsis; /* 显示省略号 */
                            white-space: nowrap; /* 不换行 */
                            padding-left: 8px;
                            padding-right: 8px;
                        }
                    }
                }
            }
        }
    }
}
</style>






