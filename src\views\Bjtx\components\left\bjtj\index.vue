
<template>
    
    <Panel size="big" class="bjtj" title="人员类型统计">
        <div class="statistics-container">
            <!-- 圆环图区域 -->
            <div class="chart-wrapper">
                <div class="donut-chart">
                    <!-- 使用ECharts圆环图 -->
                    <Echarts :options="chartOptions" class="ring-chart" />
                    <!-- 中心文字 -->
                    <div class="chart-center">
                        <div class="total-label">人员总数</div>
                        <div class="total-value">{{ staffData.total }}</div>
                    </div>
                </div>
            </div>
            
            <!-- 统计信息 -->
            <div class="statistics-info">
                <div class="stat-item">
                    <span class="stat-dot" style="background: #D75F9D;"></span>
                    <span class="stat-label">在园访客数</span>
                    <span class="stat-value">{{ staffData.visitors }}</span>
                    <span class="stat-percent">{{ Math.round((staffData.visitors / staffData.total) * 100) }}%</span>
                </div>
                <div class="stat-item">
                    <span class="stat-dot" style="background: #4600D4;"></span>
                    <span class="stat-label">在园员工数</span>
                    <span class="stat-value">{{ staffData.employees }}</span>
                    <span class="stat-percent">{{ Math.round((staffData.employees / staffData.total) * 100) }}%</span>
                </div>
            </div>
        </div>
    </Panel>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import { getWeather } from '@/apis';
import { useStore } from "vuex";

// 人员统计数据
const staffData = ref({
    visitors: 152,
    employees: 1278,
    total: 1430
});

const store = useStore();

watch(
    () => store.state.updateTime.active,
    (newVal) => {
        getApi();
    },
)

onMounted(() => {
    getApi();
})

const getApi = async () => {
    try {
        const res = await getWeather();
        // 后续接口出来后直接替换
        // staffData.value = res.staffStatistics || staffData.value;
    } catch (error) {
        console.log('API调用失败:', error);
    }
}

// ECharts圆环图配置
const chartOptions = computed(() => ({
    tooltip: {
        show: true,
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
        show: false
    },
    series: [{
        name: '人员类型',
        type: 'pie',
        radius: ['65%', '80%'],  // 调整饼图环形厚度，使其更细
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        silent: false,
        label: {
            show: false
        },
        labelLine: {
            show: false
        },
        emphasis: {
            disabled: false,  // 启用悬浮效果
            scale: true,      // 启用缩放效果
            scaleSize: 5,     // 缩放大小
            focus: 'self'     // 聚焦效果
        },
        itemStyle: {
            borderRadius: 0,
            borderColor: 'transparent',
            borderWidth: 0
        },
        data: [
            {
                value: staffData.value.visitors,
                name: '在园访客数',
                itemStyle: {
                    color: '#D75F9D'  // 粉紫色
                }
            },
            {
                value: staffData.value.employees,
                name: '在园员工数',
                itemStyle: {
                    color: '#4600D4'  // 深紫色
                }
            }
        ]
    }]
}));
</script>

<style lang="scss" scoped>
/* 人员类型统计组件样式 */
.bjtj {
    width: 100%;  
    max-width: 490px;  
    height: 285px;
    
    /* 容器样式 */
    .statistics-container {
        padding: 20px;
        height: 100%;
        display: flex;
        align-items: center;
        gap: 40px;
        
        /* 图表区域样式 */
        .chart-wrapper {
            flex-shrink: 0;
            
            .donut-chart {
                position: relative;
                width: 200px;  /* 保持饼图容器大小 */
                height: 200px;
                
                /* ECharts图表样式 */
                .ring-chart {
                    width: 100%;
                    height: 100%;
                    
                }
                
                /* 中心文字样式 */
                .chart-center {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    text-align: center;
                    pointer-events: none;
                    
                    .total-label {
                        color: rgba(255, 255, 255, 0.8);
                        font-size: 14px;
                        margin-bottom: 8px;
                    }
                    
                    .total-value {
                        color: #fff;
                        font-size: 36px;  /* 保持中心数字大小 */
                        font-weight: bold;
                        // font-family: "PZDBTMF", sans-serif;
                    }
                }
            }
        }
        
        /* 统计信息样式 */
        .statistics-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 24px;
            
            .stat-item {
                display: flex;
                align-items: center;
                gap: 12px;
                padding: 8px 0;
                transition: all 0.3s ease;
                
                /* 悬浮效果 */
        
                
                /* 颜色点样式 */
                .stat-dot {
                    width: 8px;
                    height: 8px;
                
                   margin-left: -30px;
                }
                
                .stat-label {
                    // color: rgba(255, 255, 255, 0.8);
                    font-size: 14px;
                    flex: 1;
                }          

                

                

            }
        }
    }
}
</style> 