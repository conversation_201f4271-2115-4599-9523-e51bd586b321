<template>
    <div class="wt-title">
        <div class="title">蓝胖子智慧园区综合态势大屏</div>
        <div class="left">
            <!-- <div class="logo">LOGO</div> -->
        </div>
        <div class="right">
            <div class="weather">
                <!-- <span>&nbsp;&nbsp;{{data.ActiveWd}}</span> -->
                <span>{{data.activeTime}}</span>
            </div>
        </div>
    </div>
</template>

<script setup>
import {reactive,onMounted,watch} from 'vue';
import moment from 'moment';
import {getWeather} from '@/apis';
import { useStore } from "vuex";

const data = reactive({
    activeWeather: '大雪',
    ActiveWd: '36',
    activeTime: '',
})

const store = useStore();
window.store=store

function numToChinese(num) {
  const chineseNumbers = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
  return chineseNumbers[num];
}

watch(
    () => store.state.updateTime.active,
    (newVal) => {
        getWeath();
    },
)

onMounted(()=>{
    setInterval(() => {
        const nowDate = new Date();
        data.activeTime = '  ' + (nowDate.getMonth() + 1) + '/' + nowDate.getDate() + '/' + nowDate.getFullYear()
         + "  "  + nowDate.getHours() + ":" + nowDate.getMinutes() + ':' + nowDate.getSeconds();
    }, 1000);
    getWeath();
})

const getWeath = async () => {
    const res = await getWeather();
    data.ActiveWd = res.temp;
    data.activeWeather = res.text;
}
</script>

<style lang="scss" scoped>
.wt-title {
    width: 80%;
    height: 84px;
    position: absolute;
    top: 0;
    left: 10%;
    z-index: 99;
    background: url("@/assets/title.png") no-repeat center center/100% 100%;
    .title {
        font-family: Source Han Sans;
        font-size: 34px;
        font-weight: bold;
        line-height: 64px;
        text-align: center;
        letter-spacing: normal;
        background: linear-gradient(180deg, #51E0FF 0%, #FFFFFF 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
    }
    .right {
        position: absolute;
        right: -10%;
        top: 30px;
        font-size: 12px;
        // background: red;
        // font-family: 'PZDBTMF';
        .weather {
            span {
            }
            img {
                width: 30px;
                vertical-align: text-bottom;
                margin-right: 10px;
            }
        }
    }
}
</style>