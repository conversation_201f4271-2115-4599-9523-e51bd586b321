<template>
    <div>
        <Panel size="big" class="hjjk-container" title="天气">
            <div class="weather-content">
                <!-- 太阳图标区域 -->
                <div class="weather-icon">
                    <div class="sun-icon">
                        <img src="@/assets/Thesun.png" alt="晴天">
                    </div>
                </div>
                
                <!-- 天气信息区域 -->
                <div class="weather-info">
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="label">天气 :</span>
                            <span class="value">晴</span>
                        </div>
                        <div class="info-item">
                            <span class="label">温度 :</span>
                            <span class="value">13°C</span>
                        </div>
                        <div class="info-item">
                            <span class="label">风向 :</span>
                            <span class="value">东南风</span>
                        </div>
                        <div class="info-item">
                            <span class="label">风速 :</span>
                            <span class="value">6km/h</span>
                        </div>
                        <div class="info-item">
                            <span class="label">PM2.5 :</span>
                            <span class="value">36μg/m³</span>
                        </div>
                        <div class="info-item">
                            <span class="label">空气质量 :</span>
                            <span class="value">良</span>
                        </div>
                    </div>
                </div>
            </div>
        </Panel>
    </div>
</template>

<script setup>
import { reactive, onMounted, watch } from 'vue'
import { getWeather } from '@/apis'
import { useStore } from 'vuex'
const store = useStore()

// 天气数据
const data = reactive({
    weather: '晴',
    temperature: '13°C',
    windDirection: '东南风',
    windSpeed: '6km/h',
    pm25: '36μg/m³',
    airQuality: '良'
})

// 获取数据的API调用
const getApi = async () => {
    try {
        const res = await getWeather()
        console.log('天气数据更新:', res)
    } catch (error) {
        console.log('天气API调用失败:', error)
    }
}

// 监听数据更新
watch(
    () => store.state.updateTime?.active,
    (newVal) => {
        if (newVal) {
            getApi()
        }
    }
)

onMounted(() => {
    getApi()
})
</script>
 
<style scoped lang="scss">
.hjjk-container {
    width: 100%;
    height: 380px;
    
    .weather-content {
        height: calc(100% - 60px);
        display: flex;
        flex-direction: column;
        padding: 20px;
        
        .weather-icon {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 30px;
            margin-top: 35px;
            min-height: 120px; 
            
            .sun-icon {
                width: 100px;
                height: 100px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: radial-gradient(circle, rgba(255, 193, 7, 0.2) 0%, transparent 70%);
                border-radius: 50%;
                flex-shrink: 0; 
                
                img {
                    width: 82px;
                    height: 81px;
                    object-fit: contain;
                    display: block;
                }
            }
        }
        
        .weather-info {
            flex: 1;
            width: 85%;
            align-items: center;
            display: flex;
            justify-content: center;
            align-self: center;
            
            .info-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 15px 30px;
                width: 100%;
                
                .info-item {
                    display: flex;
                    align-items: center;
                    
                    &:nth-child(even) {
                        justify-content: flex-end;
                    }
                    
                    .label {
                        color: rgba(255, 255, 255, 0.8);
                        font-size: 14px;
                    }
                    
                    .value {
                        font-size: 16px;
                        font-weight: bold;
                        font-family: 'Arial', sans-serif;
                        margin-left: 10px;
                    }
                }
            }
        }
    }
}
</style>






