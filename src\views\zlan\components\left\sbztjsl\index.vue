<!--
 * @Author: hkh <EMAIL>
 * @Date: 2024-07-18 10:00:22
 * @LastEditors: hkh <EMAIL>
 * @LastEditTime: 2024-07-29 17:42:43
 * @FilePath: \chengde-computer-room\src\views\zlan\components\left\sbztjsl\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <Panel size="big" class="sbztjsl" title="设备状态及数量统计">
        <div class="pan-content">
            <div class="left">
                <div class="title">PUE</div>
                <!-- <div class="chart" id="sbztjsl-chart"></div> -->
                <!-- <HCharts class="chart" :options="options" /> -->
                <div class="chart">
                    <div class="tubiao">
                        <!-- <img class="zhishipan" src="@/assets/zlan/zhishipan.png" alt=""> -->
                        <!-- <img class="zhizhen" :style="'transform: rotate(-45deg);transform-origin: bottom center;'" src="@/assets/zlan/zhizhen.png" alt=""> -->
                    </div>
                </div>
            </div>
            <div class="right">
                <div class="PLF">
                    <div class="title">PLF</div>
                    <div class="content">
                        <div>
                            <!-- <img src="@/assets/zlan/dian.png" alt="" /> -->
                        </div>
                        <div class="name">供电负载系数</div>
                        <div class="value">{{data.PLF}}</div>
                    </div>
                </div>
                <div class="CLF">
                    <div class="title">CLF</div>
                    <div class="content">
                        <div>
                            <!-- <img src="@/assets/zlan/xue.png" alt="" /> -->
                        </div>
                        <div class="name">制冷负载系数</div>
                        <div class="value">{{data.CLF}}</div>
                    </div>
                </div>
            </div>
        </div>
    </Panel>
</template>

<script setup>
import { reactive, computed, onMounted,watch } from "vue";
import Highcharts from "highcharts/highstock";
import {getWeather} from '@/apis';
import { useStore } from "vuex";
const data = reactive({
    "PUE": 0, //PUE
    "PLF": 0, //PLF 供电系数
    "CLF": 0 //CLF 制冷系数
});
const store = useStore();
watch(
    () => store.state.updateTime.active,
    (newVal) => {
        getApi();
    },
)
onMounted(()=>{
  getApi();
})
const getApi = async () => {
  const res = await getWeather();
  data.PUE = res[0].PUE;
  data.PLF = res[0].PLF;
  data.CLF = res[0].CLF;
}
</script>

<style lang="scss" scoped>
.sbztjsl {
    width: 660px;
    height: 300px;
    .pan-content {
        display: flex;
        justify-content: space-between;
        padding: 20px 0;
        .title {
            border-bottom: 1px solid #fff;
            font-size: 20px;
        }
    }
    .chart {
        width: 240px;
        height: 200px;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        .tubiao {
            width: 210px;
            height: 120px;
            justify-content: center;
            align-items: center;
            position: relative;
            .zhishipan {
                width: 230px;
                height: 120px;
            }
            .zhizhen {
                width: 230px;
                height: 120px;
                position: absolute;
                z-index: 1;
                bottom: 5px;
                left: 0;
            }
        }
    }
    .left {
        width: 240px;
        height: 210px;
    }
    .right {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 210px;
        & > div {
            width: 360px;
            height: 100px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 4px 4px 4px 4px;
            padding: 10px;
            .title {
                margin-bottom: 10px;
            }
            .content {
                width: 340px;
                height: 41px;
                padding: 0 10px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 4px;
                img {
                    height: 27px;
                    margin-top: 5px;
                }
                .name {
                    flex: 1;
                    margin: 0 20px;
                }
            }
        }
    }
}
</style>