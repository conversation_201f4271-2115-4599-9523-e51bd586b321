<!--
 * @Author: hkh <EMAIL>
 * @Date: 2024-07-17 17:04:30
 * @LastEditors: hkh <EMAIL>
 * @LastEditTime: 2024-07-19 13:13:04
 * @FilePath: \chengde-computer-room\src\components\LayoutRight\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="wt-layout-right">
        <slot>
            <router-view />
        </slot>
    </div>
</template>

<script setup>

</script>

<style lang="scss" scoped>
.wt-layout-right {
    position: absolute;
    z-index: 2;
    right: 0px;
    top: 8%;
    // top: 100px;
    // background: rgba(23,35,48,0.5);
    width: 491px;
    // height: calc(100% - 100px);
    height: 92%;
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    
}
</style>