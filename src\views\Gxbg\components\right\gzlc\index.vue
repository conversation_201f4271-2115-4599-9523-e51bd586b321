<template>
    <div>
        <Panel size="big" class="gzlc-container" title="会议室使用率">
            <div class="content-wrapper">
                <!-- 圆环图区域 -->
                <div class="chart-section">
                    <div class="pie-chart-container">
                        <Echarts :options="chartOptions" class="pie-chart" />
                        <div class="chart-center-text">
                            <div class="center-title">会议室总数</div>
                            <div class="center-number-row">
                                <span class="center-number">54</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 统计数据区域 -->
                <div class="stats-section">
                    <div class="stat-item">
                        <div class="stat-icon" style="background: #29CC82;"></div>
                        <div class="stat-content">
                            <div class="stat-label">空闲数</div>
                            <div class="stat-value">44</div>
                            <div class="stat-percent">81%</div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon" style="background: #4600D4;"></div>
                        <div class="stat-content">
                            <div class="stat-label">占用数</div>
                            <div class="stat-value">10</div>
                            <div class="stat-percent">19%</div>
                        </div>
                    </div>
                </div>
            </div>
        </Panel>
    </div>
</template>

<script setup>
import { reactive, computed, onMounted, watch } from 'vue'
import { getWeather } from '@/apis'
import { useStore } from 'vuex'
const store = useStore()

// 数据定义
const data = reactive({
    totalRooms: 54,
    idleRooms: 44,  // 空闲数
    occupiedRooms: 10  // 占用数
})

// 图表配置
const chartOptions = computed(() => ({
    tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c}间 ({d}%)'
    },
    series: [{
        name: '会议室使用率',
        type: 'pie',
        radius: ['70%', '85%'],
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        label: {
            show: false
        },
        labelLine: {
            show: false
        },
        emphasis: {
            scale: false
        },
        data: [
            {
                value: data.idleRooms,
                name: '空闲数',
                itemStyle: {
                    color: '#29CC82'
                }
            },
            {
                value: data.occupiedRooms,
                name: '占用数',
                itemStyle: {
                    color: '#4600D4'
                }
            }
        ]
    }]
}))

// 获取数据的API调用
const getApi = async () => {
    try {
        const res = await getWeather()
        console.log('会议室使用率数据更新:', res)
    } catch (error) {
        console.log('会议室使用率API调用失败:', error)
    }
}

// 监听数据更新
watch(
    () => store.state.updateTime?.active,
    (newVal) => {
        if (newVal) {
            getApi()
        }
    }
)

onMounted(() => {
    getApi()
})
</script>

<style scoped lang="scss">
.gzlc-container {
    width: 100%;
    height: 380px;
    
    .content-wrapper {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 100%;
        padding: 20px 10px;
        
        .chart-section {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            
            .pie-chart-container {
                width: 220px;  /* 参考工位使用情况组件的大小 */
                height: 220px; /* 参考工位使用情况组件的大小 */
                position: relative;
                
                .pie-chart {
                    width: 100%;
                    height: 100%;
                }
                
                .chart-center-text {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    text-align: center;
                    pointer-events: none;
                    
                    .center-title {
                        color: rgba(255, 255, 255, 0.7);
                        font-size: 14px;
                        font-weight: 400;
                        line-height: 1.2;
                        margin-bottom: 8px;
                    }
                    
                    .center-number-row {
                        display: flex;
                        align-items: baseline;
                        justify-content: center;
                        gap: 4px;
                    }
                    
                    .center-number {
                        color: #ffffff;
                        font-size: 26px;
                        font-weight: bold;
                        line-height: 1;
                        font-family: 'Arial', sans-serif;
                    }
                }
            }
        }
        
        .stats-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 30px;  
            padding-left: 20px;
            
            .stat-item {
                display: flex;
                align-items: center;
                gap: 12px;
                
                .stat-icon {
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    flex-shrink: 0;
                }
                
                .stat-content {
                    display: flex;
                    align-items: center;
                    gap: 15px;
                    
                    .stat-label {
                        color: rgba(255, 255, 255, 0.9);
                        font-size: 14px;
                        font-weight: 400;
                        white-space: nowrap;
                    }
                    
                    .stat-value {
                        // color: #ffffff;
                        font-size: 14px;
                        // font-weight: bold;
                        // font-family: 'Arial', sans-serif;
                        line-height: 1;
                    }
                    
                    .stat-percent {
                        // color: rgba(255, 255, 255, 0.7);
                        font-size: 14px;
                        margin-left: 35px;
                    }
                }
            }
        }
    }
}
</style>






