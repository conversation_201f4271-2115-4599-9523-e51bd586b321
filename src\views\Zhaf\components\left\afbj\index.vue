<!--
 * @Description: 安保统计组件 - 显示安保人员的圆环统计
-->
<template>
    <Panel size="big" class="afbj" title="安保统计">
        <div class="stats-container">
            <div class="ring-chart-container">
                <!-- 使用ECharts圆环进度图 -->
                <div class="chart-wrapper">
                    <Echarts :options="chartOptions" class="ring-chart" />
                    <div class="chart-center">
                        <div class="sub-label">人员总数</div>
                        <div class="main-number">{{data.totalGuards}}</div>
                       
                    </div>
                </div>
            </div>
            <!-- 详细信息 -->
            <div class="stats-info">
                <div class="info-row">
                    <span class="dot green"></span>
                    <span class="label">在园人数</span>
                    <span>{{data.onDuty}}</span>
                </div>
 
            </div>
        </div>
    </Panel>
</template>

<script setup>
import { reactive, onMounted, watch, computed } from "vue";
import { getWeather } from '@/apis';
import { useStore } from "vuex";

const data = reactive({
    totalGuards: 16,
    onDuty: 9,
    patrolling: 7,
    percentage: 71 // 71.01%的显示
});

const store = useStore();

// ECharts圆环图配置
const chartOptions = computed(() => ({
    tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [{
        name: '安保统计',
        type: 'pie',
        radius: ['75%', '90%'],
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        label: {
            show: false
        },
        labelLine: {
            show: false
        },
        itemStyle: {
            borderRadius: 8,
            borderColor: 'rgba(0,0,0,0)',
            borderWidth: 2
        },
        data: [
            {
                value: data.onDuty,
                name: '在岗人员',
                itemStyle: {
                    color: '#00FF7F'
                }
            },
            {
                value: data.totalGuards - data.onDuty,
                name: '离岗人员',
                itemStyle: {
                    color: 'rgba(255, 255, 255, 0.1)'
                }
            }
        ]
    }]
}));

watch(
    () => store.state.updateTime.active,
    (newVal) => {
        getApi();
    },
)

onMounted(() => {
    getApi();
})

const getApi = async () => {
    try {
        const res = await getWeather();
        // 更新安保统计数据
    } catch (error) {
        console.log('API调用失败:', error);
    }
}
</script>

<style lang="scss" scoped>
.afbj {
    width: 490px;
    height: 285px;
  
    .stats-container {
        padding: 20px;
        height: 100%;
        display: flex;
        align-items: center;
        gap: 30px;
        
        .ring-chart-container {
            display: flex;
            justify-content: center;
            align-items: center;
            
            .chart-wrapper {
                position: relative;
                width: 180px;
                height: 180px;
                
                .ring-chart {
                    width: 100%;
                    height: 100%;
                }
                
                .chart-center {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    text-align: center;
                    
                    .main-number {
                        color: #00FF7F;
                        font-size: 36px;
                        font-weight: bold;
                        font-family: "PZDBTMF";
                        line-height: 1;
                        margin-bottom: 8px;
                    }
                    
                    .sub-label {
                        color: #fff;
                        font-size: 14px;
                        opacity: 0.8;
                    }
                }
            }
        }
        
        .stats-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 15px;
            
            .info-row {
                display: flex;
                align-items: center;
                gap: 10px;
                padding: 12px 0;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                
                &:last-child {
                    border-bottom: none;
                }
                
                .dot {
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    
                    &.green {
                        background: #00FF7F;
                    }
                    
                    &.orange {
                        background: #FFA500;
                    }
                }
                
                .label {
                    color: #fff;
                    font-size: 14px;
                    opacity: 0.8;
                    flex: 1;
                }
                
                .value {
                    color: #00FF7F;
                    font-size: 18px;
                    font-weight: bold;
                    font-family: "PZDBTMF";
                }
            }
        }
    }
}
</style> 