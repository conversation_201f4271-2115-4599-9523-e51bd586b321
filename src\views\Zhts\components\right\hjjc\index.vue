<template>
    <Panel size="big" class="hjjc" title="环境监测">
        <div class="environment-monitor">
            <!-- 自定义美观表格 -->
            <div class="custom-table">
                <!-- 表头 -->
                <div class="table-header">
                    <div class="header-cell location-col">位置</div>
                    <div class="header-cell temperature-col">温度</div>
                    <div class="header-cell humidity-col">湿度</div>
                    <div class="header-cell pm25-col">PM2.5</div>
                    <div class="header-cell quality-col">甲醛</div>
                </div>

                <!-- 表体 -->
                <div class="table-body">
                    <div
                        v-for="(item, index) in data.monitorList"
                        :key="index"
                        class="table-row"
                        :class="{ 'row-even': index % 2 === 0 }"
                    >
                        <div class="body-cell location-col">{{ item.location }}</div>
                        <div class="body-cell temperature-col">{{ item.temperature }}</div>
                        <div class="body-cell humidity-col">{{ item.humidity }}</div>
                        <div class="body-cell pm25-col">{{ item.pm25 }}</div>
                        <div class="body-cell quality-col">{{ item.quality }}</div>
                    </div>
                </div>
            </div>

            <!-- <div class="environment-summary">
                <div class="summary-card">
                    <div class="summary-title">平均温度</div>
                    <div class="summary-value">{{data.averageTemp}}°C</div>
                    <div class="summary-trend">
                        <span class="trend-icon">↑</span>
                        <span class="trend-text">较昨日 +2°C</span>
                    </div>
                </div>
                <div class="summary-card">
                    <div class="summary-title">平均湿度</div>
                    <div class="summary-value">{{data.averageHumidity}}%</div>
                    <div class="summary-trend">
                        <span class="trend-icon">→</span>
                        <span class="trend-text">与昨日持平</span>
                    </div>
                </div>
                <div class="summary-card">
                    <div class="summary-title">空气质量</div>
                    <div class="summary-value">{{data.airQuality}}</div>
                    <div class="summary-trend">
                        <span class="trend-icon">↓</span>
                        <span class="trend-text">较昨日优化</span>
                    </div>
                </div>
            </div> -->
        </div>
    </Panel>
</template>

<script setup>
import { reactive, onMounted, watch } from "vue";
import { monitorReal } from "@/apis";
import { useStore } from "vuex";

const data = reactive({
    monitorList: [
        {
            location: "建筑1",
            temperature: "25°C",
            humidity: "33%RH",
            pm25: "0.2μg/m³",
            quality: "0.2mg/m³",
        },
        {
            location: "建筑2",
            temperature: "27°C",
            humidity: "12%RH",
            pm25: "22μg/m³",
            quality: "0.5mg/m³",
        },
        {
            location: "建筑3",
            temperature: "35°C",
            humidity: "43%RH",
            pm25: "0.03μg/m³",
            quality: "2mg/m³",
        },
        {
            location: "建筑4",
            temperature: "43°C",
            humidity: "50%RH",
            pm25: "0.8μg/m³",
            quality: "44mg/m³",
        },
        {
            location: "建筑5",
            temperature: "21°C",
            humidity: "3%RH",
            pm25: "0.2μg/m³",
            quality: "5mg/m³",
        },
        {
            location: "建筑6",
            temperature: "11°C",
            humidity: "3%RH",
            pm25: "0.2μg/m³",
            quality: "0.24mg/m³",
        },
        {
            location: "建筑7",
            temperature: "25°C",
            humidity: "3%RH",
            pm25: "0.2μg/m³",
            quality: "0.2mg/m³",
        },
    ],
    averageTemp: 27,
    averageHumidity: 65,
    airQuality: "良好",
});

const store = useStore();

watch(
    () => store.state.updateTime.active,
    (newVal) => {
        getApi();
    },
);

onMounted(() => {
    getApi();
   
});

const getApi = async () => {
    try {
        const res = await monitorReal();
        console.log(res);
        data.monitorList = res.data.map((item) => {
            return {
                location: item.regionPathName,
                temperature: item.TEMP + "℃",
                humidity: item.HUMI + "%",
                pm25: item.PM25 + "μg/m³",
                quality: item.PM10 + "μg/m³",
            };
        });
    } catch (error) {
        console.log("API调用失败:", error);
    }
};
 


</script>

<style lang="scss" scoped>
.hjjc {
    width: 490px;
    height: 450px;

    .environment-monitor {
        padding: 15px;
        height: 100%;
        display: flex;
        flex-direction: column;

        /* 自定义表格样式 */
        .custom-table {
            width: 100%;
            flex: 1;
            display: flex;
            flex-direction: column;

            /* 表头样式 */
            .table-header {
                display: flex;

                .header-cell {
                    padding: 12px 10px;
                    color: rgba(255, 255, 255, 0.9);
                    font-size: 14px;
                    font-weight: 500;
                    text-align: left;

                    &.location-col {
                        width: 20%;
                        min-width: 60px;
                    }

                    &.temperature-col {
                        width: 20%;
                        min-width: 60px;
                    }

                    &.humidity-col {
                        width: 20%;
                        min-width: 60px;
                    }

                    &.pm25-col {
                        width: 20%;
                        min-width: 80px;
                    }

                    &.quality-col {
                        width: 20%;
                        min-width: 80px;
                    }
                }
            }

            /* 表体样式 */
            .table-body {
                flex: 1;
                overflow-y: auto;
                border-radius: 0 0 6px 6px;

                /* 隐藏滚动条但保持滚动功能 */
                &::-webkit-scrollbar {
                    display: none;
                }
                scrollbar-width: none;
                -ms-overflow-style: none;

                .table-row {
                    display: flex;
                    transition: all 0.2s ease;

                    &:last-child {
                        border-bottom: none;
                    }

                    .body-cell {
                        padding: 12px 10px;
                        color: rgba(255, 255, 255, 0.8);
                        font-size: 13px;
                        line-height: 1.4;
                        display: flex;
                        align-items: center;

                        &.location-col {
                            width: 20%;
                            min-width: 60px;
                            font-weight: 500;
                            color: rgba(255, 255, 255, 0.9);
                        }

                        &.temperature-col {
                            width: 20%;
                            min-width: 60px;
                            color: rgba(255, 255, 255, 0.8);
                        }

                        &.humidity-col {
                            width: 20%;
                            min-width: 60px;
                            color: rgba(255, 255, 255, 0.8);
                        }

                        &.pm25-col {
                            width: 20%;
                            min-width: 80px;
                            color: rgba(255, 255, 255, 0.8);
                        }

                        &.quality-col {
                            width: 20%;
                            min-width: 80px;
                            color: rgba(255, 255, 255, 0.8);
                        }
                    }
                }
            }
        }

        .environment-summary {
            display: flex;
            justify-content: space-between;
            gap: 15px;
            margin-top: 15px;

            .summary-card {
                flex: 1;
                padding: 15px;
                background: linear-gradient(
                    135deg,
                    rgba(0, 191, 255, 0.1),
                    rgba(0, 191, 255, 0.05)
                );
                border-radius: 8px;
                border: 1px solid rgba(0, 191, 255, 0.3);
                text-align: center;

                .summary-title {
                    color: #fff;
                    font-size: 14px;
                    opacity: 0.8;
                    margin-bottom: 8px;
                }

                .summary-value {
                    color: #00bfff;
                    font-size: 24px;
                    font-weight: bold;
                    font-family: "PZDBTMF";
                    margin-bottom: 8px;
                }

                .summary-trend {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 4px;

                    .trend-icon {
                        color: #4caf50;
                        font-size: 16px;
                        font-weight: bold;
                    }

                    .trend-text {
                        color: #fff;
                        font-size: 12px;
                        opacity: 0.6;
                    }
                }
            }
        }
    }
}


</style>
