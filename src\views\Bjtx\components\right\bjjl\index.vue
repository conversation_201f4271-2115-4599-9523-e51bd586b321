
<template>

    <Panel size="big" class="bjjl" title="车辆通行记录">
        <div class="record-container">
     
            <div class="custom-table">
                <!-- 表头 -->
                <div class="table-header">
                    <div class="header-cell plate-col">车牌号</div>
                    <div class="header-cell time-col">时间</div>
                    <div class="header-cell gate-col">出入闸机</div>
                    <div class="header-cell status-col">状态</div>
                </div>
                
                <!-- 表体 -->
                <div class="table-body">
                    <div 
                        v-for="(record, index) in recordList" 
                        :key="index"
                        class="table-row"
                        :class="{ 'row-even': index % 2 === 0 }"
                    >
                        <div class="body-cell plate-col">{{ record.plate }}</div>
                        <div class="body-cell time-col">{{ record.time }}</div>
                        <div class="body-cell gate-col">{{ record.gate }}</div>
                        <div class="body-cell status-col">
                            <span class="status-badge" :class="record.statusClass">
                                {{ record.status }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </Panel>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import { getWeather } from '@/apis';
import { useStore } from "vuex";

// 车辆通行记录数据
const recordList = ref([
    { plate: '浙A1234', time: '2025-04-16 15:37:52', gate: '主入口车闸', status: '进入', statusClass: 'status-in' },
    { plate: '浙B6789', time: '2025-04-16 15:35:20', gate: '侧门车闸', status: '离开', statusClass: 'status-out' },
    { plate: '浙C1111', time: '2025-04-16 15:33:15', gate: '北门车闸', status: '进入', statusClass: 'status-in' },
    { plate: '浙D2222', time: '2025-04-16 15:30:45', gate: '南门车闸', status: '离开', statusClass: 'status-out' },
    { plate: '浙E3333', time: '2025-04-16 15:28:30', gate: '主入口车闸', status: '进入', statusClass: 'status-in' },
    { plate: '浙F4444', time: '2025-04-16 15:25:18', gate: '侧门车闸', status: '进入', statusClass: 'status-in' },
    { plate: '浙G5555', time: '2025-04-16 15:22:55', gate: '北门车闸', status: '离开', statusClass: 'status-out' },
    { plate: '浙H6666', time: '2025-04-16 15:20:12', gate: '南门车闸', status: '进入', statusClass: 'status-in' }
]);

const store = useStore();

watch(
    () => store.state.updateTime.active,
    (newVal) => {
        getApi();
    },
)

onMounted(() => {
    getApi();
})

const getApi = async () => {
    try {
        const res = await getWeather();
        // 后续接口出来后直接替换
        // recordList.value = res.vehicleRecords || recordList.value;
    } catch (error) {
        console.log('API调用失败:', error);
    }
}
</script>

<style lang="scss" scoped>

.bjjl {
    width: 100%;
    max-width: 490px;
    height: 285px;
    
    /* 记录容器样式 */
    .record-container {
        padding: 15px;
        height: calc(100%);
        overflow: hidden;
        
        /* 自定义表格样式 */
        .custom-table {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            
            /* 表头样式 */
            .table-header {
                display: flex;
                
                .header-cell {
                    padding: 10px 4px;
                    // color: rgba(255, 255, 255, 0.9);
                    font-size: 14px;
                    text-align: center;
                    
                    &.plate-col {
                        width: 18%;
                        min-width: 60px;
                    }
                    
                    &.time-col {
                        width: 35%;
                        min-width: 140px;
                    }
                    
                    &.gate-col {
                        width: 32%;
                        min-width: 120px;
                    }
                    
                    &.status-col {
                        width: 15%;
                        min-width: 60px;
                        text-align: center;
                    }
                }
            }
            
            /* 表体样式 */
            .table-body {
                flex: 1;
                overflow-y: auto;
          
                border-radius: 0 0 6px 6px;
                // background: red;
                margin-top: 10px;
           
                &::-webkit-scrollbar {
                    display: none;
                }
                scrollbar-width: none;
                -ms-overflow-style: none;
                
                .table-row {
                    display: flex;
                    
                    &:last-child {
                        border-bottom: none;
                    }
                    


                    .body-cell {
                        padding: 10px 10px;
                        color: rgba(255, 255, 255, 0.8);
                        font-size: 13px;
                        line-height: 1.4;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        text-align: center;
                        // background: red;
                        &.plate-col {
                            width: 18%;
                            min-width: 60px;
                            font-weight: 500;
                            color: rgba(255, 255, 255, 0.9);
                        }
                        
                        &.time-col {
                            width: 35%;
                            min-width: 140px;
                            color: #fff;
                            font-family: 'Courier New', monospace;
                            white-space: nowrap; /* 时间在一行显示 */
                            // background: red;
                        }
                        
                        &.gate-col {
                            width: 32%;
                            min-width: 120px;
                            overflow: hidden; /* 隐藏超出内容 */
                            text-overflow: ellipsis; /* 显示省略号 */
                            white-space: nowrap; /* 不换行 */
                            padding-left: 8px;
                            padding-right: 8px;
                        }
                        
                        &.status-col {
                            width: 15%;
                            min-width: 60px;
                            justify-content: center;
                       
                        }
                    }
                }
            }
        }
        
    }
    

}
</style> 