<!--
 * @Author: 智慧园区开发团队
 * @Date: 2025-01-01
 * @Description: 告警状态统计组件 - 显示圆环统计图
-->
<template>
    <!-- 使用Panel组件包裹 -->
    <Panel size="big" class="afmj" title="告警状态统计">
        <div class="alarm-status-container">
            <!-- 圆环图区域 -->
            <div class="chart-wrapper">
                <div class="donut-chart">
                    <!-- 使用ECharts圆环图 -->
                    <Echarts :options="chartOptions" class="ring-chart" />
                    <!-- 中心文字 -->
                    <div class="chart-center">
                        <div class="total-label">告警总数</div>
                        <div class="total-value">52</div>
                    </div>
                </div>
            </div>
            
            <!-- 统计列表 -->
            <div class="status-list">
                <div class="status-item" v-for="(item, index) in statusList" :key="index">
                    <span class="status-dot" :style="{backgroundColor: item.color}"></span>
                    <span class="status-name">{{ item.name }}</span>
                    <span class="status-value">{{ item.value }}</span>
                    <span class="status-percent">{{ item.percent }}%</span>
                </div>
            </div>
        </div>
    </Panel>
</template>

<script setup>

import Echarts from '@/components/Echarts';
import { ref, computed, reactive, onMounted, onUnmounted, watch } from "vue";
import { getWeather } from '@/apis';
import { useStore } from "vuex";

// 状态列表数据
const statusList = ref([
    { name: '处理中', value: 30, percent: 2, color: '#ff9500' },
    { name: '待处理', value: 50, percent: 10, color: '#D2653B' },
    { name: '已处理', value: 46, percent: 88, color: '#29CC82' }
]);

const data = reactive({
    totalAlarms: 52
});

const store = useStore();

// ECharts圆环图配置
const chartOptions = computed(() => ({
    tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [{
        name: '告警状态统计',
        type: 'pie',
        radius: ['75%', '90%'],
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        label: {
            show: false
        },
        labelLine: {
            show: false
        },
        itemStyle: {
            borderRadius: 8,
            borderColor: 'rgba(0,0,0,0)',
            borderWidth: 2
        },
        data: statusList.value.map(item => ({
            value: item.value,
            name: item.name,
            itemStyle: {
                color: item.color
            }
        }))
    }]
}));

// 监听更新时间变化
watch(
    () => store.state.updateTime.active,
    (newVal) => {
        getApi();
    },
)

// 组件挂载时执行
onMounted(() => {
    getApi();
})

// API调用函数
const getApi = async () => {
    try {
        const res = await getWeather();
        // 更新告警状态数据
        // data.totalAlarms = res.totalAlarms || 52;
        // statusList.value = res.alarmStatus || statusList.value;
    } catch (error) {
        console.log('API调用失败:', error);
    }
}
</script>

<style lang="scss" scoped>
/* 告警状态统计组件样式 */
.afmj {
    width: 490px;  /* 组件宽度 */
    height: 285px; /* 组件高度 */
  
    /* 容器样式 */
    .alarm-status-container {
        padding: 20px;
        height: 100%;
        display: flex;
        align-items: center;
        gap: 40px;
        
        /* 图表区域样式 */
        .chart-wrapper {
            flex-shrink: 0;
            
            .donut-chart {
                position: relative;
                width: 180px;
                height: 180px;
                
                /* ECharts图表样式 */
                .ring-chart {
                    width: 100%;
                    height: 100%;
                }
                
                /* 中心文字样式 */
                .chart-center {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    text-align: center;
                    pointer-events: none;
                    
                    .total-label {
                        color: rgba(255, 255, 255, 0.6);
                        font-size: 14px;
                        margin-bottom: 8px;
                    }
                    
                    .total-value {
                      
                        font-size: 26px;
                        font-weight: bold;
                       
                    }
                }
            }
        }
        
        /* 统计列表样式 */
        .status-list {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 16px;
            
            .status-item {
                display: flex;
                align-items: center;
                padding: 12px 16px;
                background: rgba(255, 255, 255, 0.05);
                border-radius: 8px;
              
              
                
                /* 状态点样式 */
                .status-dot {
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    margin-right: 12px;
                }
                
                /* 状态名称样式 */
                .status-name {
                    color: rgba(255, 255, 255, 0.8);
                    font-size: 14px;
                    margin-right: auto;
                }
                
                /* 状态数值样式 */
                .status-value {
                    color: #fff;
                    font-size: 16px;
                    font-weight: 500;
                    margin-right: 16px;
                    min-width: 30px;
                    text-align: right;
                }
                
                /* 百分比样式 */
                .status-percent {
                    color: #00bfff;
                    font-size: 14px;
                    font-weight: 500;
                    min-width: 45px;
                    text-align: right;
                }
            }
        }
    }
}
</style> 