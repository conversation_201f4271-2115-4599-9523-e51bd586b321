<template>
    <div>
        <Panel size="big" class="sbbj-container" title="已对接设备">
            <div class="content-wrapper">
                <div class="chart-container">
                    <Echarts :options="chartOptions" class="bar-chart" />
                </div>
            </div>
        </Panel>
    </div>
</template>

<script setup>
import { reactive, computed, onMounted, watch } from 'vue'
import { getWeather } from '@/apis'
import { useStore } from 'vuex'

// 导入图表组件
import Echarts from '@/components/Echarts'

const store = useStore()

// 数据定义 - 根据图片显示的数据调整
const data = reactive({
    deviceTypes: [
        { name: '门禁', value: 45 },
        { name: '摄像头', value: 85 },
        { name: '传感器', value: 90 },
        { name: '照明', value: 55 }
    ]
})

// 图表配置 - 完全按照图片样式设置
const chartOptions = computed(() => ({
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'shadow'
        }
    },
    grid: {
        left: '8%',
        right: '8%',
        bottom: '15%',
        top: '10%',
        containLabel: true
    },
    xAxis: {
        type: 'category',
        data: data.deviceTypes.map(item => item.name),
        axisLabel: {
            color: '#fff',
            fontSize: 13,
            margin: 15
        },
        axisLine: {
            show: false
        },
        axisTick: {
            show: false
        }
    },
    yAxis: {
        type: 'value',
        max: 100,
        interval: 20,
        axisLabel: {
            color: 'rgba(255, 255, 255, 0.6)',
            fontSize: 12,
            formatter: '{value}个'
        },
        axisLine: {
            show: false
        },
        axisTick: {
            show: false
        },
        splitLine: {
            show: false
        }
    },
    series: [
        {
            type: 'bar',
            data: data.deviceTypes.map(item => item.value),
            itemStyle: {
                color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                        {
                            offset: 0,
                            color: '#80FDC2' // 顶部颜色
                        },
                        {
                            offset: 1,
                            color: '#00A7FE' // 底部颜色
                        }
                    ]
                },
                borderRadius: [2, 2, 0, 0] // 顶部圆角
            },
            barWidth: 18, // 设置柱状图宽度，让它看起来很细
            emphasis: {
                disabled: true // 禁用悬停效果
            }
        }
    ]
}))

// 获取数据的API调用
const getApi = async () => {
    try {
        const res = await getWeather()
        // 当真实接口可用时，替换下面的数据
        // data.deviceTypes = res.deviceTypes || data.deviceTypes
    } catch (error) {
        console.log('设备对接API调用失败:', error)
    }
}

// 监听数据更新
watch(
    () => store.state.updateTime.active,
    (newVal) => {
        getApi()
    }
)

onMounted(() => {
    getApi()
})
</script>
 
<style scoped lang="scss">
.sbbj-container {
    width: 100%;
    height: 380px;
    
    .content-wrapper {
        height: 100%;
        padding: 20px 10px 10px 10px;
        
        .chart-container {
            height: 100%;
            
            .bar-chart {
                width: 100%;
                height: 100%;
            }
        }
    }
}
</style>






