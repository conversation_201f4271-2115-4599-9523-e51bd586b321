<!--
 * @Author: hkh <EMAIL>
 * @Date: 2024-07-18 09:51:51
 * @LastEditors: hkh <EMAIL>
 * @LastEditTime: 2024-07-19 13:14:40
 * @FilePath: \chengde-computer-room\src\components\Panel\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="Panel" :class="className">
    <div class="title" :class="size">
      {{ title }}
    </div>
    <slot> </slot>
  </div>
</template>

<script>
export default {
  name: "Panel",
  props: {
    title: String,
    size: String,
    className: String,
  },
};
</script>

<style lang="scss" scoped>
.Panel {
  background: rgba(23, 35, 48, 0.5);
  border-radius: 10px 10px 10px 10px;
  border: 0px solid #ffffff;
  padding: 20px;
  box-sizing: border-box;
  position: relative;
  .title {
    width: 100%;
    height: 35px;
    line-height: 23px;
    padding-left: 20px;
    font-family: PingFang SC, PingFang SC;
    font-weight: bold;
    font-size: 20px;
    color: #ffffff;
    text-align: left;
    font-style: normal;
    text-transform: none;
    // width: 100%;
    // height: 34px;
    // line-height: 34px;
    // padding-left: 36px;
    // color: rgba(255, 255, 255, 1);
    // font-size: 22px;
    // display: flex;
    // align-items: center;
    // justify-content: space-between;
    // font-family: YSBiaoTi;
    // letter-spacing: 3px;
    // background: red;
    background: url("") no-repeat center center/100% 100%;
    &.big {
      background: url("../../assets/小标题icon.png") no-repeat left center;
      background-size: 15px; 
      padding-left: 25px; // 为图标和文字之间留出间距
      display: flex;
      align-items: center;
      // background: red;\\
      // background: rgba(0, 128, 0, 0.216);
    }
    &.medium {
      background: url("./bg_2.png") no-repeat center center/100% 100%;
    }
    &.small {
      background: url("./bg_3.png") no-repeat center center/100% 100%;
    }
  }
}
</style>
