import BlankLayout from "@/layouts/blank";
import { createRouter, createWeb<PERSON>ash<PERSON><PERSON><PERSON> } from "vue-router";
import { getToken } from "@/apis";

const routes = [
    {
        path: "",
        redirect: "/zhts",
    },
    {
        path: "",
        component: BlankLayout,
        children: [
            {
                path: "/zlan",
                name: "zlan",
                component: () => import("@/views/zlan"),
            },
        ],
    },
    {
        path: "",
        component: BlankLayout,
        children: [
            {
                path: "/zhts",
                name: "zhts",
                component: () => import("@/views/Zhts"),
            },
        ],
    },{
        path: "",
        component: BlankLayout,
        children: [
            {
                path: "/zhdt",
                name: "zhdt",
                component: () => import("@/views/Zhdt"),
            },
        ],
    },{
        path: "",
        component: BlankLayout,
        children: [
            {
                path: "/zhaf",
                name: "zhaf",
                component: () => import("@/views/Zhaf"),
            },
        ],
    },{
        path: "",
        component: Blank<PERSON>ayout,
        children: [
            {
                path: "/bjtx",
                name: "bjtx",
                component: () => import("@/views/Bjtx"),
            },
        ],
    },{
        path: "",
        component: BlankLayout,
        children: [
            {
                path: "/sbss",
                name: "sbss",
                component: () => import("@/views/Sbss"),
            },
        ],
    },{
        path: "",
        component: BlankLayout,
        children: [
            {
                path: "/nhgl",
                name: "nhgl",
                component: () => import("@/views/Nhgl"),
            },
        ],
    },{
        path: "",
        component: BlankLayout,
        children: [
            {
                path: "/gxbg",
                name: "gxbg",
                component: () => import("@/views/Gxbg"),
            },
        ],
    },{
        path: "",
        component: BlankLayout,
        children: [
            {
                path: "/hjkj",
                name: "hjkj",
                component: () => import("@/views/Hjkj"),
            },
        ],
    },
];

export const router = createRouter({
    history: createWebHashHistory(),
    routes: routes,
});
