<template>
    <div>
        <Panel size="big" class="nhbj-container" title="楼层用水量排名">
            <div class="content-wrapper">
                <div class="table-container">
                    <div class="table-header">
                        <span class="header-item">楼层</span>
                        <span class="header-item">楼层属性</span>
                        <span class="header-item">用水量</span>
                    </div>
                    <div class="table-body">
                        <div 
                            v-for="(item, index) in data.floorData" 
                            :key="index"
                            class="table-row"
                            :class="{
                                'rank-first': index === 0,
                                'rank-second': index === 1,
                                'rank-third': index === 2
                            }"
                        >
                            <!-- 前三名不显示数字，由背景图片替代 -->
                            <span class="row-item floor-num" v-if="index > 2">{{ item.floor }}</span>
                            <span class="row-item floor-num rank-bg" v-else></span>
                            <span class="row-item floor-type">{{ item.type }}</span>
                            <span class="row-item usage-value">{{ item.usage }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </Panel>
    </div>
</template>

<script setup>
import { reactive, onMounted, watch } from 'vue'
import { getWeather } from '@/apis'
import { useStore } from 'vuex'

const store = useStore()

// 数据定义
const data = reactive({
    floorData: [
        { floor: '1', type: '1F', usage: '320' },
        { floor: '2', type: '2F', usage: '250' },
        { floor: '3', type: '3F', usage: '210' },
        { floor: '4', type: '4F', usage: '190' },
        { floor: '5', type: '5F', usage: '180' },
        { floor: '6', type: '6F', usage: '155' }
    ]
})

// 获取数据的API调用
const getApi = async () => {
    try {
        const res = await getWeather()
        // 当真实接口可用时，替换下面的数据
    } catch (error) {
        console.log('楼层用水量API调用失败:', error)
    }
}

// 监听数据更新
watch(
    () => store.state.updateTime.active,
    (newVal) => {
        getApi()
    }
)

onMounted(() => {
    getApi()
})
</script>
 
<style scoped lang="scss">
.nhbj-container {
    width: 100%;
    height: 380px;
    
    .content-wrapper {
        height: 100%;
        padding: 10px 0 30px 0;
        
        .table-container {
            height: calc(100% - 20px);
            display: flex;
            flex-direction: column;
            
            .table-header {
                display: flex;
                align-items: center;
                height: 40px;
                border-radius: 4px;
                margin-bottom: 10px;
                padding: 0 15px;
                
                .header-item {
                    color: #fff;
                    font-size: 14px;
                    font-weight: bold;
                    text-align: center;
                    
                    &:nth-child(1) { width: 60px; } // 楼层
                    &:nth-child(2) { flex: 1; } // 楼层属性
                    &:nth-child(3) { width: 80px; } // 用水量
                }
            }
            
            .table-body {
                flex: 1;
                overflow-y: auto;
                max-height: 225px;
                &::-webkit-scrollbar {
                    display: none;
                }
                .table-row {
                    display: flex;
                    align-items: center;
                    height: 45px;
                    padding: 0 15px;
                    transition: background-color 0.3s ease;
                    position: relative;
                    
                    &:hover {
                        background: rgba(255, 255, 255, 0.05);
                    }
                    
                    // 第一名背景图片
                    &.rank-first {
                        .rank-bg {
                            background-image: url('@/assets/1 (1).png');
                            background-size: contain;
                            background-repeat: no-repeat;
                            background-position: center;
                            height: 35px;
                            display: block;
                        }
                    }
                    
                    // 第二名背景图片
                    &.rank-second {
                        .rank-bg {
                            background-image: url('@/assets/2.png');
                            background-size: contain;
                            background-repeat: no-repeat;
                            background-position: center;
                            height: 35px;
                            display: block;
                        }
                    }
                    
                    // 第三名背景图片
                    &.rank-third {
                        .rank-bg {
                            background-image: url('@/assets/3.png');
                            background-size: contain;
                            background-repeat: no-repeat;
                            background-position: center;
                            height: 35px;
                            display: block;
                        }
                    }
                    
                    .row-item {
                        color: #fff;
                        font-size: 13px;
                        text-align: center;
                        
                        &.floor-num {
                            width: 60px;
                            color: rgba(255, 255, 255, 0.7);
                        }
                        
                        &.floor-type {
                            flex: 1;
                            font-family: 'Arial', sans-serif;
                        }
                        
                        &.usage-value {
                            width: 80px;
                            font-family: 'Arial', sans-serif;
                            font-weight: bold;
                        }
                    }
                }
            }
        }
    }
}

// 滚动条样式
.table-body::-webkit-scrollbar {
    width: 4px;
}

.table-body::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
}

.table-body::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    
    &:hover {
        background: rgba(255, 255, 255, 0.5);
    }
}
</style>






