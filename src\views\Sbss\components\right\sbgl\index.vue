<template>
    <div>
        <Panel size="big" class="sbgl-container" title="设备告警统计">
            <div class="content-wrapper">
                <div class="chart-section">
                    <div class="pie-chart-container">
                        <Echarts :options="chartOptions" class="pie-chart" />
                    </div>
                </div>
                
                <div class="alarm-legend">
                    <div class="legend-item">
                        <div class="legend-dot" style="background-color: #B31B1B;"></div>
                        <div class="legend-content">
                            <span class="legend-name">告警数量</span>
                            <span class="legend-count">{{ data.alarmCount }}</span>
                        </div>
                    </div>
                    <div class="legend-item">
                        <div class="legend-dot" style="background-color: #1BB388;"></div>
                        <div class="legend-content">
                            <span class="legend-name">消警数量</span>
                            <span class="legend-count">{{ data.clearCount }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </Panel>
    </div>
</template>

<script setup>
import { reactive, computed, onMounted, watch } from 'vue'
import { getWeather } from '@/apis'
import { useStore } from 'vuex'

// 导入图表组件
import Echarts from '@/components/Echarts'

const store = useStore()

// 数据定义 - 按照图片显示的数据
const data = reactive({
    alarmCount: 12,  // 告警数量
    clearCount: 88   // 消警数量
})

// 图表配置 - 完全按照图片样式设置
const chartOptions = computed(() => ({
    tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [{
        name: '设备告警',
        type: 'pie',
        radius: '75%', // 改为实心圆饼图，不是圆环
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        label: {
            show: false
        },
        labelLine: {
            show: false
        },
        emphasis: {
            disabled: false // 启用悬停效果以支持tooltip
        },
        data: [
            {
                value: data.clearCount,
                name: '消警数量',
                itemStyle: {
                    color: '#1BB388' // 绿色 - 消警 (占大部分)
                }
            },
            {
                value: data.alarmCount,
                name: '告警数量',
                itemStyle: {
                    color: '#B31B1B' // 红色 - 告警 (占小部分)
                }
            }
        ]
    }]
}))

// 获取数据的API调用
const getApi = async () => {
    try {
        const res = await getWeather()
        // 当真实接口可用时，替换下面的数据
        // data.alarmCount = res.alarmCount || data.alarmCount
        // data.clearCount = res.clearCount || data.clearCount
    } catch (error) {
        console.log('设备告警API调用失败:', error)
    }
}

// 监听数据更新
watch(
    () => store.state.updateTime.active,
    (newVal) => {
        getApi()
    }
)

onMounted(() => {
    getApi()
})
</script>
 
<style scoped lang="scss">
.sbgl-container {
    width: 100%;
    height: 380px;
    
    .content-wrapper {
        height: 100%;
        display: flex;
        align-items: center;
        gap: 50px;
        padding: 30px 20px;
        
        .chart-section {
            flex: 0 0 180px; // 固定圆环图宽度
            display: flex;
            justify-content: center;
            align-items: center;
            
            .pie-chart-container {
                width: 180px;
                height: 180px;
                
                .pie-chart {
                    width: 100%;
                    height: 100%;
                }
            }
        }
        
        .alarm-legend {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 25px;
            
            .legend-item {
                display: flex;
                align-items: center;
                gap: 15px;
                
                .legend-dot {
                    width: 12px;
                    height: 12px;
                    border-radius: 50%;
                    flex-shrink: 0;
                }
                
                .legend-content {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    width: 100%;
                    
                    .legend-name {
                        color: rgba(255, 255, 255, 0.9);
                        font-size: 15px;
                        font-weight: 400;
                    }
                    
                    .legend-count {
                        color: #fff;
                        font-size: 18px;
                        font-weight: bold;
                        font-family: 'Arial', sans-serif;
                    }
                }
            }
        }
    }
}
</style>






