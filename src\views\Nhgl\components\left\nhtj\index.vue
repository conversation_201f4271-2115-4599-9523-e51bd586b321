<template>
    <div>
        <Panel size="big" class="nhtj-container" title="日用电量度数">
            <div class="content-wrapper">
                <div class="info-box">
                    <div class="date">{{ data.currentDate }}</div>
                    <div class="value">{{ data.electricValue }}度</div>
                </div>
                
                <div class="chart-container">
                    <div class="circle-chart">
                        <Echarts :options="chartOptions" class="pie-chart" />
                        <div class="chart-center">
                            <div class="electric-icon"></div>
                        </div>
                    </div>
                </div>
            </div>
        </Panel>
    </div>
</template>

<script setup>
import { reactive, computed, onMounted, watch } from 'vue'
import { getWeather } from '@/apis'
import { useStore } from 'vuex'

// 导入图表组件
import Echarts from '@/components/Echarts'

const store = useStore()

// 数据定义
const data = reactive({
    currentDate: '2025-05-26',
    electricValue: 1234,
    usageRate: 75 // 使用率百分比
})

// 图表配置
const chartOptions = computed(() => ({
    tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [{
        name: '用电量',
        type: 'pie',
        radius: ['70%', '85%'],
        center: ['50%', '50%'],
        startAngle: 90,
        avoidLabelOverlap: false,
        label: {
            show: false
        },
        labelLine: {
            show: false
        },
        emphasis: {
            disabled: false
        },
        data: [
            {
                value: data.usageRate,
                name: '已使用',
                itemStyle: {
                    color: '#4AC3FF'
                }
            },
            {
                value: 100 - data.usageRate,
                name: '未使用',
                itemStyle: {
                    color: 'rgba(255, 255, 255, 0.1)'
                }
            }
        ]
    }]
}))

// 获取数据的API调用
const getApi = async () => {
    try {
        const res = await getWeather()
        // 当真实接口可用时，替换下面的数据
    } catch (error) {
        console.log('日用电量API调用失败:', error)
    }
}

// 监听数据更新
watch(
    () => store.state.updateTime.active,
    (newVal) => {
        getApi()
    }
)

onMounted(() => {
    getApi()
})
</script>
 
<style scoped lang="scss">
.nhtj-container {
    width: 100%;
    height: 380px;
    
    .content-wrapper {
        height: 100%;
        position: relative;
        padding: 20px;
        
        .info-box {
            position: absolute;
            top: 110px;
            left: 30px;
            // background: rgba(0, 0, 0, 0.3);
          
            border-radius: 8px;
            padding: 15px 20px;
            min-width: 120px;
            
            .date {
                color: rgba(255, 255, 255, 0.8);
                font-size: 14px;
                margin-bottom: 8px;
            }
            
            .value {
                color: #fff;
                font-size: 24px;
                font-weight: bold;
                font-family: 'Arial', sans-serif;
            }
        }
        
        .chart-container {
            position: absolute;
            top: 50%;
            left: 70%;
            transform: translate(-50%, -50%);
            
            .circle-chart {
                width: 200px;
                height: 200px;
                position: relative;
                
                .pie-chart {
                    width: 100%;
                    height: 100%;
                }
                
                                    .chart-center {
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        text-align: center;
                        
                        .electric-icon {
                            width: 40px;
                            height: 40px;
                            background-image: url('@/assets/用电量同比icon.png');
                            background-size: contain;
                            background-repeat: no-repeat;
                            background-position: center;
                            display: inline-block;
                        }
                    }
            }
        }
    }
}
</style>






