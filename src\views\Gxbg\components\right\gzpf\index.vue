<template>
    <div>
        <Panel size="big" class="gzpf-container" title="功能区域分布">
            <!-- 功能区域网格布局 -->
            <div class="area-grid">
                <!-- 第一行 -->
                <div class="grid-row">
                    <div class="area-item">
                        <div class="area-icon">
                            <img src="@/assets/Restaurantarea.png" alt="餐厅区">
                        </div>
                        <div class="area-text">
                            <div class="area-name">餐厅区</div>
                            <div class="area-size">2000m²</div>
                        </div>
                    </div>
                    <div class="area-item">
                        <div class="area-icon">
                            <img src="@/assets/ExhibitionHallArea.png" alt="展厅区">
                        </div>
                        <div class="area-text">
                            <div class="area-name">展厅区</div>
                            <div class="area-size">4500m²</div>
                        </div>
                    </div>
                </div>
                
                <!-- 第二行 -->
                <div class="grid-row">
                    <div class="area-item">
                        <div class="area-icon">
                            <img src="@/assets/ReceptionArea.png" alt="接待区">
                        </div>
                        <div class="area-text">
                            <div class="area-name">接待区</div>
                            <div class="area-size">12900m²</div>
                        </div>
                    </div>
                    <div class="area-item">
                        <div class="area-icon">
                            <img src="@/assets/Greenarea.png" alt="绿化区">
                        </div>
                        <div class="area-text">
                            <div class="area-name">绿化区</div>
                            <div class="area-size">500m²</div>
                        </div>
                    </div>
                </div>
                
                <!-- 第三行 -->
                <div class="grid-row">
                    <div class="area-item">
                        <div class="area-icon">
                            <img src="@/assets/Restarea.png" alt="休息区">
                        </div>
                        <div class="area-text">
                            <div class="area-name">休息区</div>
                            <div class="area-size">1000m²</div>
                        </div>
                    </div>
                    <div class="area-item">
                        <div class="area-icon">
                            <img src="@/assets/Others.png" alt="其他">
                        </div>
                        <div class="area-text">
                            <div class="area-name">其他</div>
                            <div class="area-size">12000m²</div>
                        </div>
                    </div>
                </div>
            </div>
        </Panel>
    </div>
</template>

<script setup>
import { reactive, onMounted, watch } from 'vue'
import { getWeather } from '@/apis'
import { useStore } from 'vuex'
const store = useStore()

// 功能区域数据
const data = reactive({
    areas: [
        { name: '餐厅区', size: '2000m²', icon: '餐厅区icon.png' },
        { name: '展厅区', size: '4500m²', icon: '展厅区icon.png' },
        { name: '接待区', size: '12900m²', icon: '接待区icon.png' },
        { name: '绿化区', size: '500m²', icon: '绿化区icon.png' },
        { name: '休息区', size: '1000m²', icon: '休息区icon.png' },
        
        { name: '其他', size: '12000m²', icon: '其他icon.png' }
    ]
})

// 获取数据的API调用
const getApi = async () => {
    try {
        const res = await getWeather()
        console.log('功能区域分布数据更新:', res)
    } catch (error) {
        console.log('功能区域分布API调用失败:', error)
    }
}

// 监听数据更新
watch(
    () => store.state.updateTime?.active,
    (newVal) => {
        if (newVal) {
            getApi()
        }
    }
)

onMounted(() => {
    getApi()
})
</script>
 
<style scoped lang="scss">
.gzpf-container {
    width: 100%;
    height: 380px;
    
    .area-grid {
        height: calc(100% - 60px);
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding: 20px;
        gap: 20px;
        margin-top: 15px;
        .grid-row {
            display: flex;
            justify-content: space-between;
            gap: 20px;
            margin-top: 5px;
            .area-item {
                flex: 1;
                display: flex;
                align-items: center;
                gap: 15px;
                cursor: pointer;
                transition: all 0.3s ease;
                
                &:hover {
                    transform: translateY(-2px);
                    
                    .area-icon img {
                        transform: scale(1.05);
                    }
                }
                
                .area-icon {
                    width: 77px;
                    height: 77px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-shrink: 0;
                    // background: red;
                    
                    img {
                        width: 100%;
                        height:100%;
                        object-fit: contain;
                        transition: transform 0.3s ease;
                    }
                }
                
                .area-text {
                    flex: 1;
                    // background: red;
                    
                    .area-name {
                        color: #ffffff;
                        font-size: 16px;
                        font-weight: 500;
                        line-height: 1.2;
                        margin-bottom: 4px;
                    
                    }
                    
                    .area-size {
                        // color: #00d4ff;
                        font-size: 24px;
                        // font-weight: bold;
                        font-family: 'Arial', sans-serif;
                    }
                }
            }
        }
    }
}
</style>






