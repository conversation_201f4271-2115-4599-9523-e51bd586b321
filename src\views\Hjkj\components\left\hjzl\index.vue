<template>
    <div>
        <Panel size="big" class="hjzl-container" title="会议室使用情况">
            <div class="room-content">
                <div class="room-table">
                    <div class="table-header">
                        <div class="header-cell"></div>
                        <div class="header-cell">温度</div>
                        <div class="header-cell">湿度</div>
                        <div class="header-cell">PM2.5</div>
                        <div class="header-cell">甲醛</div>
                    </div>
                    <div class="table-body">
                        <div class="table-row" v-for="(room, index) in roomData" :key="index">
                            <div class="table-cell room-name">{{ room.name }}</div>
                            <div class="table-cell">{{ room.temperature }}</div>
                            <div class="table-cell">{{ room.humidity }}</div>
                            <div class="table-cell">{{ room.pm25 }}</div>
                            <div class="table-cell">{{ room.formaldehyde }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </Panel>
    </div>
</template>

<script setup>
import { reactive, onMounted, watch } from 'vue'
import { getWeather } from '@/apis'
import { useStore } from 'vuex'
const store = useStore()

// 会议室数据
const roomData = reactive([
    {
        name: '会议室1',
        temperature: '25°C',
        humidity: '30%RH',
        pm25: '0.2μg/m³',
        formaldehyde: '0.2mg/m³'
    },
    {
        name: '会议室2',
        temperature: '27°C',
        humidity: '12%RH',
        pm25: '22μg/m³',
        formaldehyde: '0.5mg/m³'
    },
    {
        name: '会议室3',
        temperature: '35°C',
        humidity: '4%RH',
        pm25: '0.02μg/m³',
        formaldehyde: '2mg/m³'
    },
    {
        name: '会议室4',
        temperature: '43°C',
        humidity: '50%RH',
        pm25: '0.5μg/m³',
        formaldehyde: '64mg/m³'
    },
    {
        name: '会议室5',
        temperature: '31°C',
        humidity: '3%RH',
        pm25: '0.2μg/m³',
        formaldehyde: '5mg/m³'
    },
    {
        name: '会议室6',
        temperature: '13°C',
        humidity: '3%RH',
        pm25: '0.2μg/m³',
        formaldehyde: '0.24mg/m³'
    },
    {
        name: '会议室7',
        temperature: '26°C',
        humidity: '3%RH',
        pm25: '0.2μg/m³',
        formaldehyde: '0.2mg/m³'
    }
])

// 获取数据的API调用
const getApi = async () => {
    try {
        const res = await getWeather()
        console.log('会议室数据更新:', res)
    } catch (error) {
        console.log('会议室API调用失败:', error)
    }
}

// 监听数据更新
watch(
    () => store.state.updateTime?.active,
    (newVal) => {
        if (newVal) {
            getApi()
        }
    }
)

onMounted(() => {
    getApi()
})
</script>
 
<style scoped lang="scss">
.hjzl-container {
    width: 100%;
    height: 380px;
    
    .room-content {
        height: calc(100% - 60px);
        padding: 15px;
        
        .room-table {
            height: 100%;
            display: flex;
            flex-direction: column;
            
            .table-header {
                display: flex;
                // background: rgba(0, 212, 255, 0.2);
                border-radius: 4px 4px 0 0;
                padding: 10px 0;
                
                .header-cell {
                    flex: 1;
                    text-align: center;
                    // color: #00d4ff;
                    font-size: 14px;
                    font-weight: bold;
                    
                    &:first-child {
                        flex: 1.2;
                    }
                }
            }
            
            .table-body {
                flex: 1;
                overflow-y: auto;
                
                .table-row {
                    display: flex;
                    padding: 8px 0;
                    // border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                    // transition: background-color 0.3s ease;
                    
         
                    .table-cell {
                        flex: 1;
                        text-align: center;
                        color: #ffffff;
                        font-size: 13px;
                        padding: 5px;
                        
                        &.room-name {
                            flex: 1.2;
                            // color: #00d4ff;
                            // font-weight: bold;
                        }
                    }
                }
            }
        }
    }
}

// 自定义滚动条样式
.table-body::-webkit-scrollbar {
    width: 4px;
}


</style>






