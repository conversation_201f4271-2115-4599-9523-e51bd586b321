<template>
    <Panel size="big" class="sbyxzt" title="设备在线率">
        <div class="device-status-container">
            <div class="status-grid">
                <div class="status-item" v-for="(item, index) in data.statusList" :key="index">
                    <div class="status-circle">
                        <div class="circle-progress">
                            <svg width="100" height="100" viewBox="0 0 100 100">
                                <!-- 背景圆环 -->
                                <circle cx="50" cy="50" r="40" 
                                    stroke="rgba(255,255,255,0.1)" 
                                    stroke-width="6" 
                                    fill="none"/>
                                <!-- 进度圆环 -->
                                <circle cx="50" cy="50" r="40" 
                                    :stroke="item.color" 
                                    stroke-width="6" 
                                    fill="none"
                                    :stroke-dasharray="circumference"
                                    :stroke-dashoffset="getOffset(item.value)"
                                    stroke-linecap="round"
                                    transform="rotate(-90 50 50)"
                                    class="progress-ring"/>
                            </svg>
                            <div class="circle-content">
                                <div class="percentage">{{item.value}}%</div>
                            </div>
                        </div>
                    </div>
                    <div class="device-name">{{item.name}}</div>
                </div>
            </div>
        </div>
    </Panel>
</template>

<script setup>
import { reactive, onMounted, watch, computed } from "vue";
import { getWeather } from '@/apis';
import { useStore } from "vuex";

const data = reactive({
    statusList: [
        { name: '梯控', value: 100, color: '#00BFFF' },
        { name: '背景音乐', value: 100, color: '#00BFFF' },
        { name: '空调', value: 99, color: '#00BFFF' },
        { name: '停车场', value: 100, color: '#00BFFF' },
        { name: '门禁', value: 97, color: '#00BFFF' },
        { name: '摄像头', value: 98, color: '#00BFFF' }
    ]
});

const store = useStore();

// 计算圆环进度
const circumference = 2 * Math.PI * 40; // r = 40

const getOffset = (value) => {
    return circumference - (value / 100) * circumference;
};

watch(
    () => store.state.updateTime.active,
    (newVal) => {
        getApi();
    },
)

onMounted(() => {
    getApi();
})

const getApi = async () => {
    try {
        const res = await getWeather();
        // 更新数据
        // data.statusList = res.deviceStatus || data.statusList;
    } catch (error) {
        console.log('API调用失败:', error);
    }
}
</script>

<style lang="scss" scoped>
.sbyxzt {
    width: 490px;
    height: 380px;
    
    .device-status-container {
        padding: 20px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(2, 1fr);
            gap: 30px 40px;
            width: 100%;
            max-width: 450px;
            
            .status-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                
                .status-circle {
                    margin-bottom: 15px;
                    
                    .circle-progress {
                        position: relative;
                        width: 100px;
                        height: 100px;
                        
                        svg {
                            width: 100%;
                            height: 100%;
                            
                            .progress-ring {
                                transition: stroke-dashoffset 0.5s ease;
                            }
                        }
                        
                        .circle-content {
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                            text-align: center;
                            
                            .percentage {
                                color: #00BFFF;
                                font-size: 20px;
                                font-weight: bold;
                               
                               
                            }
                        }
                    }
                }
                
                .device-name {
                    color: #fff;
                    font-size: 14px;
                    font-weight: 400;
                    text-align: center;
                    opacity: 0.9;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    max-width: 100px;
                }
            }
        }
    }
    
    /* 响应式设计 */
    @media (max-width: 768px) {
        .device-status-container {
            padding: 15px;
            
            .status-grid {
                gap: 20px 30px;
                max-width: 350px;
                
                .status-item {
                    .status-circle .circle-progress {
                        width: 80px;
                        height: 80px;
                        
                        .circle-content .percentage {
                            font-size: 16px;
                        }
                    }
                    
                    .device-name {
                        font-size: 12px;
                        max-width: 80px;
                    }
                }
            }
        }
    }
    
    @media (max-width: 480px) {
        .device-status-container {
            padding: 10px;
            
            .status-grid {
                gap: 15px 20px;
                max-width: 280px;
                
                .status-item {
                    .status-circle .circle-progress {
                        width: 70px;
                        height: 70px;
                        
                        .circle-content .percentage {
                            font-size: 14px;
                        }
                    }
                    
                    .device-name {
                        font-size: 11px;
                        max-width: 70px;
                    }
                }
            }
        }
    }
}
</style> 