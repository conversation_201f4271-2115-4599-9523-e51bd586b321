<template>
    <div>
        <LayoutLeft>
            <Sbztjsl />
            <Sbzt />
            <Wxgd />
            <Gjgd />
        </LayoutLeft>
         <LayoutRight>
            <Zylyl />
            <Gjgl />
            <Sjlb />
        </LayoutRight>
    </div>
</template>

<script setup>
import LayoutLeft from '@/components/LayoutLeft';
import LayoutRight from '@/components/LayoutRight';
import Sbztjsl from './components/left/sbztjsl'
import Gjgd from './components/left/gjgd'
import Sbzt from './components/left/sbzt'
import Wxgd from './components/left/wxgd'
import Zylyl from './components/right/zylyl'
import Sjlb from './components/right/sjlb'
import Gjgl from './components/right/gjgl'
</script>

<style lang="scss" scoped>

</style>