<template>
    <div>
        <Panel size="big" class="gztj-container" title="园区人流量">
            <div class="content-wrapper">
                <!-- 圆环图区域 -->
                <div class="chart-section">
                    <div class="pie-chart-container">
                        <Echarts :options="chartOptions" class="pie-chart" />
                        <div class="chart-center-text">
                            <div class="center-title">当日人流量</div>
                        </div>
                    </div>
                </div>
                
                <!-- 统计数据区域 -->
                <div class="stats-section">
                    <div class="stat-item">
                        <div class="stat-icon"></div>
                        <div class="stat-content">
                            <div class="stat-label">入园人数</div>
                            <div class="stat-value">{{ data.totalPeople }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </Panel>
    </div>
</template>

<script setup>
import { reactive, computed, onMounted, watch } from 'vue'
import { dayPersonnelStatistics } from '@/apis'
import { useStore } from 'vuex'
const store = useStore()

// 数据定义
const data = reactive({
    totalPeople: 327,
    maxCapacity: 400 // 最大容量，用于计算百分比
})

// 计算百分比
const percentage = computed(() => {
    return Math.round((data.totalPeople / data.maxCapacity) * 100)
})

// 图表配置
const chartOptions = computed(() => ({
    tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [{
        name: '人流量',
        type: 'pie',
        radius: ['70%', '85%'],
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        label: {
            show: false
        },
        labelLine: {
            show: false
        },
        emphasis: {
            scale: false
        },
        data: [
            {
                value: data.totalPeople,
                name: '已入园',
                itemStyle: {
                    color: '#00D478'
                }
            },
            {
                value: data.maxCapacity - data.totalPeople,
                name: '剩余容量',
                itemStyle: {
                    color: 'rgba(255, 255, 255, 0.1)'
                }
            }
        ]
    }]
}))


const getDayPersonnelStatistics = async () => {
    const res = await dayPersonnelStatistics();
    console.log(res);
};
// 监听数据更新
watch(
    () => store.state.updateTime?.active,
    (newVal) => {
        if (newVal) {
          
            getDayPersonnelStatistics()
        }
    }
)


onMounted(() => {
 
    getDayPersonnelStatistics()
})
</script>
 
<style scoped lang="scss">
.gztj-container {
    width: 100%;
    height: 380px;
    
    .content-wrapper {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 100%;
        padding: 20px 10px;
        
        .chart-section {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            
            .pie-chart-container {
                width: 220px;  /* 调整为与参考页面一致的大小 */
                height: 220px; /* 调整为与参考页面一致的大小 */
                position: relative;
                
                .pie-chart {
                    width: 100%;
                    height: 100%;
                }
                
                .chart-center-text {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    text-align: center;
                    pointer-events: none;
                    
                    .center-title {
                        color: rgba(255, 255, 255, 0.9);
                        font-size: 16px;  /* 稍微增大字体 */
                        font-weight: 500; /* 调整字体粗细 */
                        line-height: 1.2;
                    }
                }
            }
        }
        
        .stats-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 30px;  
            padding-left: 30px;
            
            .stat-item {
                display: flex;
                align-items: center;
                gap: 12px;
                
                .stat-icon {
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    background: #00D478;
                    box-shadow: 0 0 8px rgba(0, 212, 120, 0.3);
                    flex-shrink: 0;
                }
                
                .stat-content {
                    display: flex;
                    align-items: center;
                    gap: 15px;
                    
                    .stat-label {
                        color: rgba(255, 255, 255, 0.9);
                        font-size: 14px;
                        font-weight: 400;
                        white-space: nowrap;
                    }
                    
                    .stat-value {
                       
                        font-size: 18px;
                        font-weight: bold;
                        font-family: 'Arial', sans-serif;
                        line-height: 1;
                    }
                }
            }
        }
    }
}
</style>






