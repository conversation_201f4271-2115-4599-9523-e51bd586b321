<!--
 * @Description: 巡更统计组件 - 显示巡更相关的进度统计
-->
<template>
    <Panel size="big" class="afgl" title="巡更统计">
        <div class="patrol-container">
            <div class="patrol-stats">
                <!-- 台次统计 -->
                <div class="stat-row">
                    <div class="stat-label">台次</div>
                    <div class="stat-bar">
                        <div class="progress-bg">
                            <div class="progress-fill" :style="{width: data.stationProgress + '%'}"></div>
                        </div>
                        <div class="stat-value">{{data.stationCount}}</div>
                    </div>
                </div>
                
                <!-- 已巡行统计 -->
                <div class="stat-row">
                    <div class="stat-label">已巡行</div>
                    <div class="stat-bar">
                        <div class="progress-bg">
                            <div class="progress-fill patrol" :style="{width: data.patrolProgress + '%'}"></div>
                        </div>
                        <div class="stat-value">{{data.patrolledCount}}</div>
                    </div>
                </div>
            </div>
     
        </div>
    </Panel>
</template>

<script setup>
import { reactive, onMounted, watch } from "vue";
import { getWeather } from '@/apis';
import { useStore } from "vuex";

const data = reactive({
    stationCount: 7696,
    stationProgress: 78,
    patrolledCount: 7690,
    patrolProgress: 77,
    patrolPersonnel: 8,
    patrolPoints: 24,
    lastPatrolTime: '13:45'
});

const store = useStore();

watch(
    () => store.state.updateTime.active,
    (newVal) => {
        getApi();
    },
)

onMounted(() => {
    getApi();
})

const getApi = async () => {
    try {
        const res = await getWeather();
        // 更新巡更统计数据
    } catch (error) {
        console.log('API调用失败:', error);
    }
}
</script>

<style lang="scss" scoped>
.afgl {
    width: 490px;
    height: 285px;
  
   
    .patrol-container {
        padding: 20px;
        height: 100%;
        display: flex;
        flex-direction: column;
        gap: 20px;
        
        .patrol-stats {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 25px;
            
            .stat-row {
                display: flex;
                align-items: center;
                gap: 15px;
                
                .stat-label {
                    color: #fff;
                    font-size: 16px;
                    width: 80px;
                    opacity: 0.9;
                }
                
                .stat-bar {
                    flex: 1;
                    display: flex;
                    align-items: center;
                    gap: 15px;
                    
                    .progress-bg {
                        flex: 1;
                        height: 12px;
                        background: rgba(255, 255, 255, 0.1);
                        // border-radius: 6px;
                        overflow: hidden;
                        position: relative;
                        
                        .progress-fill {
                            height: 100%;
                            background: linear-gradient(90deg, #1E90FF, #00BFFF);
                            // border-radius: 6px;
                            transition: width 0.5s ease;
                            position: relative;
                            
                            &.patrol {
                                background: linear-gradient(90deg, #00BFFF, #87CEEB);
                            }
                            
                            &::after {
                                content: '';
                                position: absolute;
                                top: 0;
                                left: 0;
                                right: 0;
                                bottom: 0;
                                background: linear-gradient(90deg, 
                                    transparent 0%, 
                                    rgba(255,255,255,0.3) 50%, 
                                    transparent 100%);
                                animation: shimmer 2s infinite;
                            }
                        }
                    }
                    
                    .stat-value {
                        // color: #1E90FF;
                        font-size: 18px;
                        font-weight: bold;
                        // font-family: "PZDBTMF";
                        // min-width: 60px;//
                        // text-align: right;
                    }
                }
            }
        }
        
        .patrol-details {
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            
            .detail-item {
                display: flex;
                align-items: center;
                gap: 10px;
                padding: 8px 0;
                
                &:not(:last-child) {
                    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                }
                
                .detail-icon {
                    font-size: 16px;
                    width: 20px;
                }
                
                .detail-text {
                    color: #fff;
                    font-size: 14px;
                    opacity: 0.8;
                }
            }
        }
    }
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}
</style> 