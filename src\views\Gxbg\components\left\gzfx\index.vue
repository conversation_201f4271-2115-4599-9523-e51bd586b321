<template>
    <div>
        <Panel size="big" class="gzfx-container" title="会议室使用情况">
            <!-- 会议室列表区域 -->
            <div class="meeting-list">
                <div class="room-item" v-for="(room, index) in roomList" :key="index">
                    <div class="status-tag">
                        <span class="status-label">空闲</span>
                    </div>
                    <div class="room-content">
                        <div class="room-name">{{ room.name }}</div>
                        <div class="room-detail">{{ room.detail }}</div>
                    </div>
                    <div class="time-section">
                        <div class="time-label">使用时间</div>
                        <div class="time-value">{{ room.time }}</div>
                    </div>
                </div>
            </div>
        </Panel>
    </div>
</template>

<script setup>
import { reactive } from 'vue'

// 会议室数据
const roomList = reactive([
    { name: '西南大学阶梯教室302', detail: '--', time: '--' },
    { name: '一期203会议室', detail: '--', time: '--' },
    { name: '一期201会议室', detail: '--', time: '--' },
    { name: '一期202会议室', detail: '--', time: '--' },
    { name: '一期205会议室', detail: '--', time: '--' },
    { name: '一期204会议室', detail: '--', time: '--' }
])
</script>
 
<style scoped lang="scss">
.gzfx-container {
    width: 100%;
    height: 380px;
    
    .meeting-list {
        height: calc(100% - 60px);
        padding: 20px;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        gap: 12px;
        
        .room-item {
            display: flex;
            align-items: center;
            // background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 12px 16px;
            min-height: 50px;
            
            .status-tag {
                width: 34px;
height: 34px;
                background: #0080FF;
                border-radius: 4px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 16px;
                flex-shrink: 0;
                
                .status-label {
                    color: #ffffff;
                    font-size: 12px;
                    font-weight: 500;
                    // background: red;
                    // height: 100%;
                    
                }
            }
         
            .room-content {
                flex: 1;
                display: flex;
                flex-direction: column;
                justify-content: center;
                
                .room-name {
                    color: #ffffff;
                    font-size: 14px;
                    font-weight: 400;
                    line-height: 1.4;
                    margin-bottom: 2px;
                }
                
                .room-detail {
                    color: rgba(255, 255, 255, 0.6);
                    font-size: 12px;
                    line-height: 1.2;
                }
            }
            
            .time-section {
                display: flex;
                flex-direction: column;
                align-items: flex-end;
                justify-content: center;
                text-align: right;
                min-width: 80px;
                
                .time-label {
                    color: rgba(255, 255, 255, 0.8);
                    font-size: 12px;
                    line-height: 1.3;
                    margin-bottom: 2px;
                }
                
                .time-value {
                    color: rgba(255, 255, 255, 0.6);
                    font-size: 13px;
                    font-weight: 400;
                    line-height: 1.2;
                }
            }
        }
    }
}

/* 滚动条样式 */
.meeting-list::-webkit-scrollbar {
    width: 4px;
}


</style>






