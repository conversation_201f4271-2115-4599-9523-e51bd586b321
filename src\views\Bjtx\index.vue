<template>
    <div>
       
        <LayoutLeft>
            <Bjsj />   
            <Bjtj />   
            <Bjfx />   
            <Bjlx /> 
        </LayoutLeft>
     
        <LayoutRight>
            <Bjzs />    
            <Bjcl />   
            <Bjjl />    
            <Bjyj />    
        </LayoutRight>
    </div>
</template>

<script setup>
// 导入布局组件
import LayoutLeft from '@/components/LayoutLeft';
import LayoutRight from '@/components/LayoutRight';

// 导入左侧组件
import Bjsj from './components/left/bjsj'
import Bjtj from './components/left/bjtj'   
import Bjfx from './components/left/bjfx'     
import Bjlx from './components/left/bjlx'     

// 导入右侧组件  
import Bjzs from './components/right/bjzs'     
import Bjcl from './components/right/bjcl'     
import Bjjl from './components/right/bjjl'  
import Bjyj from './components/right/bjyj'     
</script>

<style scoped>
/* 报警态势页面样式 */
</style>
