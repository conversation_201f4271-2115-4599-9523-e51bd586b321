<!--
 * @Author: hkh <EMAIL>
 * @Date: 2024-07-16 17:11:39
 * @LastEditors: hkh <EMAIL>
 * @LastEditTime: 2024-07-16 17:21:22
 * @FilePath: \chengde-computer-room\src\components\BaseScene\index.vue
 * @Description: 三维场景
-->
<template>
    <div id="container">

    </div>
</template>

<script setup>
import {onMounted} from 'vue';
onMounted(() => {
    // 加载
        var loading = new VT.Loading();

        // 初始化场景
        const viewer = new VT.Viewer({
            container: "container",
            app: "5mk4jmo693q13my6gk69",
        });


        // 初始化场景数据和状态
        function initScene() {
            viewer.viewsControl.setViews(viewer.scene.option.meta.views);
            viewer.cameraControl.keypress = true;
            viewer.cameraControl.firstPersonControl.moveSpeed = 0.25;
        }

        // 场景加载完后执行事件
        viewer.on("scene.loaded", () => {
            initScene();
            window.viewer = viewer;
            setTimeout(() => {
                loading.remove();
            }, 1000);
        })
})

</script>

<style lang="scss" scoped>
#container {
    z-index: 0;
}
</style>