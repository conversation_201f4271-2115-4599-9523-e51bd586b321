<template>
    <div>
        <Panel size="big" class="sbtj-container" title="当月设备巡检任务状态统计">
            <div class="content-wrapper">
                <div class="inspection-list">
                    <div class="list-header">
                        <span class="header-item">序号</span>
                        <span class="header-item">巡检时间</span>
                        <span class="header-item">巡检状态</span>
                    </div>
                    <div class="list-body">
                        <div 
                            v-for="(item, index) in data.inspectionList" 
                            :key="index"
                            class="list-item"
                        >
                            <span class="item-index">{{ index + 1 }}</span>
                            <span class="item-time">{{ item.inspectionTime }}</span>
                            <span class="item-status" :class="getStatusClass(item.status)">
                                {{ item.status }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </Panel>
    </div>
</template>

<script setup>
import { reactive, onMounted, watch } from 'vue'
import { deviceInspectionList } from '@/apis'
import { useStore } from 'vuex'

const store = useStore()

// 数据定义
const data = reactive({
    inspectionList: [
        {
            inspectionTime: '2025-04-16 13:37:52',
            inspectionStatus: '巡检中',
            status: '巡检中'
        },
        {
            inspectionTime: '2025-04-17 13:37:52',
            inspectionStatus: '已完成',
            status: '已完成'
        },
        {
            inspectionTime: '2025-04-18 13:37:52',
            inspectionStatus: '待巡检',
            status: '待巡检'
        },
        {
            inspectionTime: '2025-04-19 13:37:52',
            inspectionStatus: '巡检中',
            status: '巡检中'
        },
        {
            inspectionTime: '2025-04-20 13:37:52',
            inspectionStatus: '已完成',
            status: '已完成'
        }, {
            inspectionTime: '2025-04-19 13:37:52',
            inspectionStatus: '巡检中',
            status: '巡检中'
        },
        {
            inspectionTime: '2025-04-20 13:37:52',
            inspectionStatus: '已完成',
            status: '已完成'
        }
    ]
})

// 获取状态样式类
const getStatusClass = (status) => {
    switch(status) {
        case '待巡检':
            return 'status-pending'
        case '巡检中':
            return 'status-processing'
        case '已完成':
            return 'status-completed'
        default:
            return ''
    }
}

// 获取数据的API调用
const getDeviceInspectionList = async () => {
    const res = await deviceInspectionList();
    console.log(res);
};

// 监听数据更新
watch(
    () => store.state.updateTime.active,
    (newVal) => {
        getDeviceInspectionList()
    }
)

onMounted(() => {
    getDeviceInspectionList()
})
</script>
 
<style scoped lang="scss">
.sbtj-container {
    width: 100%;
    height: 380px;
    // background: red;
    .content-wrapper {
        height: 100%;
        padding: 10px 0 30px 0;
        
        .inspection-list {
            height: calc(100% - 20px);
            display: flex;
            flex-direction: column;
            
            .list-header {
                display: flex;
                align-items: center;
                height: 40px;
                border-radius: 4px;
                margin-bottom: 10px;
                padding: 0 15px;
                
                .header-item {
                    color: #fff;
                    font-size: 14px;
                    font-weight: bold;
                    text-align: center;
                    
                    &:nth-child(1) { width: 60px; }
                    &:nth-child(2) { flex: 1; }
                    &:nth-child(3) { width: 80px; }
                }
            }
            
            .list-body {
                flex: 1;
                overflow-y: auto;
                max-height: 225px;
                
                .list-item {
                    display: flex;
                    align-items: center;
                    height: 45px;
                    padding: 0 15px;
              
                    
    
                    .item-index {
                        width: 60px;
                        color: rgba(255, 255, 255, 0.7);
                        font-size: 14px;
                        text-align: center;
                    }
                    
                    .item-time {
                        flex: 1;
                        color: #fff;
                        font-size: 13px;
                        font-family: 'Arial', sans-serif;
                        text-align: center;
                    }
                    
                    .item-status {
                        width: 80px;
                        font-size: 12px;
                        text-align: center;
                        padding: 4px 8px;
                        border-radius: 12px;
                        font-weight: bold;
                        
                        &.status-pending {
                            background:#B31B1B;
                            // color: #FF6B6B;
                        }
                        
                        &.status-processing {
                            background: #1B79B3;
                            // color: #FFC107;
                        }
                        
                        &.status-completed {
                            background: #1BB388;
                            // color: #1BB388;
                        }
                    }
                }
            }
        }
    }
}

// 滚动条样式
.list-body::-webkit-scrollbar {
    width: 4px;
}


</style>






