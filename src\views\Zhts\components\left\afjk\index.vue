<template>
    <Panel size="big" class="afjk" title="安防监控">
        <div class="monitor-container">
            <div class="monitor-screen">
                <div class="screen-content">
                    <div class="camera-view">
                        <!-- 模拟监控画面 -->
                        <div class="camera-info">
                            <div class="camera-id">摄像头 #001</div>
                            <div class="camera-status">● 在线</div>
                        </div>
                        <div class="scene-preview">
                            <div class="scene-bg"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="monitor-info">
                <div class="info-item">
                    <span class="label">总摄像头:</span>
                    <span class="value">{{data.totalCameras}}</span>
                </div>
                <div class="info-item">
                    <span class="label">在线:</span>
                    <span class="value online">{{data.onlineCameras}}</span>
                </div>
                <div class="info-item">
                    <span class="label">离线:</span>
                    <span class="value offline">{{data.offlineCameras}}</span>
                </div>
                <div class="info-item">
                    <span class="label">录制状态:</span>
                    <span class="value recording">● 录制中</span>
                </div>
            </div>
        </div>
    </Panel>
</template>

<script setup>
import { reactive, onMounted, watch } from "vue";
import { getWeather } from '@/apis';
import { useStore } from "vuex";

const data = reactive({
    totalCameras: 24,
    onlineCameras: 22,
    offlineCameras: 2,
    recordingStatus: true
});

const store = useStore();

watch(
    () => store.state.updateTime.active,
    (newVal) => {
        getApi();
    },
)

onMounted(() => {
    getApi();
})

const getApi = async () => {
    try {
        const res = await getWeather();
        // data.totalCameras = res.totalCameras || 24;
        // data.onlineCameras = res.onlineCameras || 22;
        // data.offlineCameras = res.offlineCameras || 2;
    } catch (error) {
        console.log('API调用失败:', error);
    }
}
</script>

<style lang="scss" scoped>
.afjk {
    width: 490px;
    height: 285px;
    .monitor-container {
        display: flex;
        padding: 20px;
        height: 100%;
        gap: 20px;
        
        .monitor-screen {
            flex: 1;
            background: #000;
            border-radius: 8px;
            border: 2px solid #333;
            overflow: hidden;
            position: relative;
            
            .screen-content {
                width: 100%;
                height: 100%;
                position: relative;
                
                .camera-view {
                    width: 100%;
                    height: 100%;
                    position: relative;
                    
                    .camera-info {
                        position: absolute;
                        top: 10px;
                        left: 10px;
                        z-index: 2;
                        
                        .camera-id {
                            color: #fff;
                            font-size: 12px;
                            background: rgba(0, 0, 0, 0.7);
                            padding: 4px 8px;
                            border-radius: 4px;
                            margin-bottom: 4px;
                        }
                        
                        .camera-status {
                            color: #00ff00;
                            font-size: 12px;
                            background: rgba(0, 0, 0, 0.7);
                            padding: 4px 8px;
                            border-radius: 4px;
                        }
                    }
                    
                    .scene-preview {
                        width: 100%;
                        height: 100%;
                        position: relative;
                        
                        .scene-bg {
                            width: 100%;
                            height: 100%;
                            background: linear-gradient(45deg, #1a1a2e, #16213e);
                            position: relative;
                            
                            &::before {
                                content: '';
                                position: absolute;
                                top: 50%;
                                left: 50%;
                                transform: translate(-50%, -50%);
                                width: 60px;
                                height: 60px;
                                border: 2px solid #00bfff;
                                border-radius: 50%;
                                animation: pulse 2s infinite;
                            }
                            
                            &::after {
                                content: '实时监控';
                                position: absolute;
                                bottom: 20px;
                                left: 50%;
                                transform: translateX(-50%);
                                color: #00bfff;
                                font-size: 14px;
                                opacity: 0.8;
                            }
                        }
                    }
                }
            }
        }
        
        .monitor-info {
            width: 200px;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            
            .info-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 12px 0;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                
                &:last-child {
                    border-bottom: none;
                }
                
                .label {
                    color: #fff;
                    font-size: 14px;
                    opacity: 0.8;
                }
                
                .value {
                    color: #00bfff;
                    font-size: 16px;
                    font-weight: bold;
                    font-family: "PZDBTMF";
                    
                    &.online {
                        color: #00ff00;
                    }
                    
                    &.offline {
                        color: #ff4444;
                    }
                    
                    &.recording {
                        color: #ff6b35;
                        animation: blink 1.5s infinite;
                    }
                }
            }
        }
    }
}

@keyframes pulse {
    0% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.1);
        opacity: 0.7;
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
}

@keyframes blink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0.5;
    }
}
</style> 