<template>
    <div>
        <Panel size="big" class="nhbg-container" title="碳减排数据采集点">
            <div class="content-wrapper">
                <!-- 左上角信息区域，按照截图红框样式 -->
                <div class="info-box">
                    <div class="date">{{ data.currentDate }}</div>
                    <div class="value">{{ data.carbonValue }}L</div>
                </div>
                
                <div class="chart-container">
                    <div class="circle-chart">
                        <Echarts :options="chartOptions" class="pie-chart" />
                        <!-- 图表中心显示水滴图标 -->
                        <div class="chart-center">
                            <div class="water-icon"></div>
                        </div>
                    </div>
                </div>
            </div>
        </Panel>
    </div>
</template>

<script setup>
import { reactive, computed, onMounted, watch } from 'vue'
import { getWeather } from '@/apis'
import { useStore } from 'vuex'

// 导入图表组件
import Echarts from '@/components/Echarts'

const store = useStore()

// 数据定义
const data = reactive({
    currentDate: '2025-05-26',
    carbonValue: 123,
    reductionRate: 80 // 减排率百分比
})

// 图表配置 - 修改颜色为#83FFC1和#00A6FF
const chartOptions = computed(() => ({
    tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [{
        name: '碳减排',
        type: 'pie',
        radius: ['70%', '85%'],
        center: ['50%', '50%'],
        startAngle: 90,
        avoidLabelOverlap: false,
        label: {
            show: false
        },
        labelLine: {
            show: false
        },
        emphasis: {
            disabled: false
        },
        data: [
            {
                value: data.reductionRate,
                name: '已减排',
                itemStyle: {
                    color: '#83FFC1'
                }
            },
            {
                value: 100 - data.reductionRate,
                name: '未减排',
                itemStyle: {
                    color: '#00A6FF'
                }
            }
        ]
    }]
}))

// 获取数据的API调用
const getApi = async () => {
    try {
        const res = await getWeather()
        // 当真实接口可用时，替换下面的数据
    } catch (error) {
        console.log('碳减排数据API调用失败:', error)
    }
}

// 监听数据更新
watch(
    () => store.state.updateTime.active,
    (newVal) => {
        getApi()
    }
)

onMounted(() => {
    getApi()
})
</script>
 
<style scoped lang="scss">
.nhbg-container {
    width: 100%;
    height: 380px;
    
    .content-wrapper {
        height: 100%;
        position: relative;
        padding: 20px;
        
        /* 左上角信息框样式，与日用电量度数组件保持一致 */
        .info-box {
            position: absolute;
            top: 110px;
            left: 30px;
            border-radius: 8px;
            padding: 15px 20px;
            min-width: 120px;
            
            .date {
                color: rgba(255, 255, 255, 0.8);
                font-size: 14px;
                margin-bottom: 8px;
            }
            
            .value {
                color: #fff;
                font-size: 24px;
                font-weight: bold;
                font-family: 'Arial', sans-serif;
            }
        }
        
        .chart-container {
            position: absolute;
            top: 50%;
            left: 70%;
            transform: translate(-50%, -50%);
            
            .circle-chart {
                width: 200px;
                height: 200px;
                position: relative;
                
                .pie-chart {
                    width: 100%;
                    height: 100%;
                }
                
                /* 图表中心显示水滴背景图片 */
                .chart-center {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    text-align: center;
                    
                    .water-icon {
                        width: 40px;
                        height: 40px;
                        background-image: url('@/assets/shui.png');
                        background-size: contain;
                        background-repeat: no-repeat;
                        background-position: center;
                        display: inline-block;
                    }
                }
            }
        }
    }
}
</style>






