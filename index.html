<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2025-04-27 16:43:39
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-04-28 17:01:28
 * @FilePath: \blue-fat-ioc\index.html
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!DOCTYPE html>
<html lang="zh" class="dark">
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0,minimum-scale=0.1,user-scalable=yes"/>
    <!--    <link rel="icon" href="/logo.png">-->
    <% if(isProduction) { %>
    <script src="./config/prod.js"></script>
    <% } else { %>
    <script src="./config/dev.js"></script>
    <% } %>
    <title></title>
    <style>
    </style>
</head>
<body>
<div id="app"></div>
<script type="module" src="./src/main.js"></script>
</body>
</html>
