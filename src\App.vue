<template>
    <router-view v-slot="{ Component }">
        <BaseTitle />
        <component :is="Component" />
        <!-- <BaseScene /> -->
        <BaseFoot />
        <div class="mengban"></div>
    </router-view>
</template>

<script setup>
// import BaseScene from '@/components/BaseScene';
import BaseTitle from "@/components/BaseTitle";
import BaseFoot from "@/components/BaseFoot";
import { useStore } from "vuex";
import { onMounted } from "vue";
onMounted(() => {
    //定时刷新数据
    setInterval(() => {
        store.commit("setUpdate");
    }, 100000);
});
const store = useStore();
</script>

<style lang="scss" scoped>
.mengban {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 0;
    top: 0;
    pointer-events: none;
    background: url("@/assets/bg.png") no-repeat center center/100% 100%;
}
</style>
