<!--
 * @Author: hkh <EMAIL>
 * @Date: 2024-07-18 14:19:19
 * @LastEditors: hkh <EMAIL>
 * @LastEditTime: 2024-07-23 13:23:03
 * @FilePath: \chengde-computer-room\src\views\zlan\components\right\gjgl\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <Panel size="big" class="gjgl" title="告警管理">
        <div class="pan-content">
            <div class="pan-box left">
                <div class="title">告警事件等级分布</div>
                <div class="content-box">
                    <!-- <ECharts :options="options" class="chart" /> -->
                    <!-- <img class="jinzita" src="@/assets/zlan/jinzita.png" alt="" /> -->
                    <div class="legend">
                        <div>一级</div>
                        <div>二级</div>
                        <div>三级</div>
                        <div>四级</div>
                        <div>五级</div>
                    </div>
                </div>
            </div>
            <div class="pan-box right">
                <div class="title">告警事件数量</div>
                <div class="content-box">
                    <ECharts :options="options1" class="chart1" />
                </div>
            </div>
        </div>
    </Panel>
</template>

<script setup>
import { reactive, computed, onMounted } from "vue";
import * as echarts from "echarts";
const data = reactive({});
const options = computed(() => {
    var value = 4.3;
    var subtext = `样本量：2233`;
    var max = 5;
    return {
        series: [
            {
                tooltip: {
                    show: false,
                },
                name: "wrap",
                type: "pie",

                center: ["50%", "60%"],
                radius: ["0%", "5%"],
                z: 5,

                labelLine: {
                    normal: {
                        show: false,
                    },
                },
                data: [
                    {
                        value: 100,
                        itemStyle: {
                            normal: {
                                color: "#072B79",
                            },
                            emphasis: {
                                color: "#072B79",
                            },
                        },
                    },
                ],
            },
            {
                tooltip: {
                    show: false,
                },
                name: "wrap",
                type: "pie",
                hoverAnimation: false,
                legendHoverLink: false,
                center: ["50%", "60%"],
                radius: ["4%", "8%"],
                z: 6,
                label: {
                    normal: {
                        show: false,
                        position: "center",
                    },
                    emphasis: {
                        show: false,
                    },
                },
                labelLine: {
                    normal: {
                        show: false,
                    },
                },
                data: [
                    {
                        value: 100,
                        itemStyle: {
                            normal: {
                                color: "white",
                            },
                            emphasis: {
                                color: "white",
                            },
                        },
                    },
                ],
            },
            {
                tooltip: {
                    show: false,
                },
                name: "刻度",
                type: "gauge",
                radius: "78%",
                z: 5,
                min: 0,
                max: 120,
                center: ["50%", "60%"],
                splitNumber: 6, //刻度数量
                startAngle: 180,
                endAngle: 0,
                axisLine: {
                    show: true,

                    lineStyle: {
                        width: 10,
                        color: [
                            [1 / 6, "#E71A6D"],
                            [2 / 6, "#F88168"],
                            [3 / 6, "red"],
                            [4 / 6, "#FBF76B"],
                            [5 / 6, "#7AD4DF"],
                            [1, "#70C27E"],
                        ],
                    },
                }, //仪表盘轴线
                axisLabel: {
                    distance: 50,
                    fontSize: 20,
                }, //刻度节点文字颜色
                axisTick: {
                    show: true,
                    lineStyle: {
                        color: "auto",
                        width: 0,
                    },
                }, //刻度样式
                splitLine: {
                    show: true,
                    length: 0,
                    lineStyle: {
                        color: "auto",
                        width: 0,
                    },
                }, //分隔线样式
                detail: {
                    show: false,
                },
                pointer: {
                    show: false,
                },
            },
            {
                tooltip: {
                    show: false,
                },
                name: "噪声",
                type: "gauge",
                radius: "75%",

                min: 0,
                max: 120,
                center: ["50%", "60%"],
                data: [
                    {
                        value: 70,
                    },
                ],
                splitNumber: 6, //刻度数量
                startAngle: 180,
                endAngle: 0,
                z: 5,
                axisLine: {
                    show: true,
                    lineStyle: {
                        width: 0,
                        color: [
                            [1 / 6, "#E71A6D"],
                            [2 / 6, "#F88168"],
                            [3 / 6, "red"],
                            [4 / 6, "#FBF76B"],
                            [5 / 6, "#7AD4DF"],
                            [1, "#70C27E"],
                        ],
                    },
                }, //仪表盘轴线
                axisLabel: {
                    show: true,
                    color: "#fff",
                    fontSize: 16,
                    distance: 20,
                    formatter: function (params) {
                        var value = params.toFixed(0);
                    },
                }, //刻度标签。

                axisTick: {
                    splitNumber: 6,
                    show: true,
                    lineStyle: {
                        color: "auto",
                        width: 3,
                    },
                    length: 20,
                }, //刻度样式
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: "auto",
                        width: 4,
                    },
                }, //分隔线样式

                itemStyle: {
                    normal: {
                        color: "white", //指针颜色
                    },
                },
                detail: {
                    show: false,
                },
                pointer: {
                    width: 13,
                    length: "60%",
                },
            },
            {
                tooltip: {
                    show: false,
                },
                name: "描述",
                type: "gauge",
                radius: "77%",

                min: 0,
                max: 100,
                center: ["50%", "60%"],

                splitNumber: 5, //刻度数量
                startAngle: 165,
                endAngle: 15,
                z: 0,
                axisLine: {
                    show: false,
                }, //仪表盘轴线

                axisLabel: {
                    show: true,
                    color: "#fff",
                    fontSize: 20,
                    distance: -44,
                    formatter: function (params) {
                        var value = params.toFixed(0);
                        if (value == 0) {
                            return "极静";
                        } else if (value == 20) {
                            return "安静";
                        } else if (value == 40) {
                            return "一般";
                        } else if (value == 60) {
                            return "较吵";
                        } else if (value == 80) {
                            return "吵闹";
                        } else if (value == 100) {
                            return "极吵";
                        } else {
                            return "";
                        }
                    },
                }, //刻度标签。

                axisTick: {
                    splitNumber: 6,
                    show: false,
                    lineStyle: {
                        color: "auto",
                        width: 0,
                    },
                    length: 20,
                }, //刻度样式
                splitLine: {
                    show: false,
                    length: 0,
                    lineStyle: {
                        color: "auto",
                        width: 0,
                    },
                }, //分隔线样式
                detail: {
                    show: false,
                },
            },
        ],
    };
});
const options1 = computed(() => {
    const dataList = [10, 22, 31, 11, 22, 14, 24, 43, 21, 32, 13, 22];
    const xList = [
        "8月",
        "9月",
        "10月",
        "11月",
        "12月",
        "1月",
        "2月",
        "3月",
        "4月",
        "5月",
        "6月",
        "7月",
    ];
    return {
        tooltip: {
            trigger: "axis",
            axisPointer: {
                type: "none",
            },
            formatter: function (prams) {
                return prams[0].name + ":" + prams[0].data;
            },
        },
        grid: {
            left: "8%",
            right: "2%",
            top: "15%",
            bottom: "15%",
        },
        xAxis: [
            {
                type: "category",
                data: xList,
                axisTick: {
                    show: false,
                    alignWithLabel: true,
                },
                minorTick: {},
                axisLabel: {
                    color: "#fff",
                    interval: 0,
                    margin: 10,
                    align: "center",
                    lineStyle: {
                        type: "dashed",
                        color: "#00ff00",
                    },
                    fontSize: window.adaption.fontSize(13),
                },
            },
        ],
        yAxis: {
            axisLine: {
                show: false,
            },
            axisTick: {
                show: false,
                color: "red",
                lineStyle: {
                    type: "dashed",
                },
            },
            splitLine: {
                color: "#fff",
                lineStyle: {
                    type: "dashed",
                    color: "rgba(255,255,255,0.3)",
                },
            },
            axisLabel: {
                color: "rgba(255,255,255,0.7)",
                fontSize: window.adaption.fontSize(13),
            },
        },
        series: [
            //上面的圆圈和文字
            {
                name: "",
                type: "pictorialBar",
                symbolSize: [10, 6],
                symbolOffset: [0, -2],
                symbolPosition: "end",
                z: 12,
                label: {
                    normal: {
                        show: true,
                        position: "top",
                        color: "#45BCFF",
                        fontWeight: "bold",
                        fontSize: window.adaption.fontSize(13),
                    },
                },
                itemStyle: {
                    normal: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                                offset: 1,
                                color: "#8ED0FF",
                                opacity: 1,
                            },
                            {
                                offset: 0,
                                color: "#1E8FE0",
                                opacity: 1,
                            },
                        ]),
                    },
                },
                data: dataList,
            },
            //下面的圆圈
            {
                name: "",
                type: "pictorialBar",
                symbolSize: [10, 6],
                symbolOffset: [0, 5],
                z: 12,
                itemStyle: {
                    normal: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                                offset: 1,
                                color: "#1E8FE0",
                                opacity: 1,
                            },
                            {
                                offset: 0,
                                color: "#1E8FE0",
                                opacity: 1,
                            },
                        ]),
                    },
                },
                data: dataList,
            },
            //最下面的圈
            {
                name: "",
                type: "pictorialBar",
                symbolSize: [15, 8],
                symbolOffset: [0, 11],
                z: 10,
                itemStyle: {
                    normal: {
                        color: "transparent",
                        borderColor: "rgba(5, 88, 140, 1)", //底部外圆圈颜色
                        borderWidth: 10,
                    },
                },
                data: dataList,
            },
            //中间的柱子
            {
                type: "bar",
                showBackground: false,
                itemStyle: {
                    normal: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                                offset: 0,
                                color: "#8ED0FF",
                                opacity: 0.85,
                            },
                            {
                                offset: 1,
                                color: "#1E8FE0",
                                opacity: 0.79,
                            },
                        ]),
                    },
                },
                barWidth: "10",
                data: dataList,
            },
        ],
    };
});
</script>

<style lang="scss" scoped>
.gjgl {
    width: 801px;
    height: 420px;
    .pan-content {
        display: flex;
        justify-content: space-between;
        padding: 20px 0;
        .pan-box {
            width: 225px;
            height: 310px;
            &.right {
                width: 531px;
            }
            .title {
                width: 100%;
                border-bottom: 1px solid #fff;
                font-size: 20px;
            }
            .content-box {
                display: flex;
                justify-content: space-between;
                padding: 13px 0;
                .chart {
                    width: 180px;
                    height: 200px;
                }
                .chart1 {
                    width: 500px;
                    height: 270px;
                }
            }
            &.left {
                .content-box {
                    flex-direction: column;
                    .jinzita {
                        width: 206px;
                        height: 206px;
                    }
                    .legend {
                        // position: absolute;
                        height: 80px;
                        top: 10px;
                        display: flex;
                        justify-content: flex-start;
                        padding-left: 10px;
                        align-items: center;
                        flex-wrap: wrap;
                        width: 100%;
                        & > div {
                            width: 33.333%;
                            position: relative;
                            font-size: 20px;
                            text-align: center;
                            &::before {
                                position: absolute;
                                content: "";
                                top: 9px;
                                left: -1px;
                                width: 8px;
                                height: 8px;
                                border-radius: 5px;
                                background: rgba(255, 255, 255, 0.5);
                                border: 1px solid #fff;
                            }
                            &:nth-child(1) {
                                &::before {
                                    background: rgba(64, 228, 208,  0.5);
                                    border: 1px solid rgba(64, 228, 208,  1);
                                }
                            }
                            &:nth-child(2) {
                                &::before {
                                    background: rgba(64, 208, 253, 0.5);
                                    border: 1px solid rgba(64, 208, 253, 1);
                                }
                            }
                            &:nth-child(3) {
                                &::before {
                                    background: rgba(64, 174, 253, 0.5);
                                    border: 1px solid rgba(64, 174, 253, 1);
                                }
                            }
                            &:nth-child(4) {
                                &::before {
                                    background: rgba(64, 117, 253, 0.5);
                                    border: 1px solid rgba(64, 117, 253, 1);
                                }
                            }
                            &:nth-child(5) {
                                &::before {
                                    background: rgba(48, 81, 255, 0.5);
                                    border: 1px solid rgba(48, 81, 255, 1);
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>