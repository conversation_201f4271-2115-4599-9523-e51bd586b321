<!--
 * @Author: hkh <EMAIL>
 * @Date: 2024-07-18 14:07:03
 * @LastEditors: hkh <EMAIL>
 * @LastEditTime: 2024-07-22 09:38:55
 * @FilePath: \chengde-computer-room\src\views\zlan\components\right\zylyl\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <Panel size="big" class="sjlb" title="事件列表">
        <el-table :data="data.tableData" border class="table" max-height="4.5rem">
            <el-table-column min-width="30" prop="level" label="事件登记" />
            <el-table-column min-width="30" prop="id" label="告警编号" />
            <el-table-column min-width="30" prop="pos" label="告警定位" />
            <el-table-column min-width="30" prop="content" label="告警内容" />
            <el-table-column min-width="30" prop="type" label="告警类型" />
            <el-table-column min-width="30" prop="start_time" label="首次告警时间" />
            <el-table-column min-width="30" prop="update_time" label="更新时间" />
        </el-table>
    </Panel>
</template>

<script setup>
import { reactive, computed, onMounted } from "vue";
const data = reactive({
    tableData: [
        {
            level: '二级',
            id: 19221,
            pos: '1号楼栋',
            content: '人工上报',
            type: '安全',
            start_time: '2024-07-19',
            update_time: '2024-07-19'
        },{
            level: '二级',
            id: 19221,
            pos: '1号楼栋',
            content: '人工上报',
            type: '安全',
            start_time: '2024-07-19',
            update_time: '2024-07-19'
        },{
            level: '二级',
            id: 19221,
            pos: '1号楼栋',
            content: '人工上报',
            type: '安全',
            start_time: '2024-07-19',
            update_time: '2024-07-19'
        },{
            level: '二级',
            id: 19221,
            pos: '1号楼栋',
            content: '人工上报',
            type: '安全',
            start_time: '2024-07-19',
            update_time: '2024-07-19'
        },{
            level: '二级',
            id: 19221,
            pos: '1号楼栋',
            content: '人工上报',
            type: '安全',
            start_time: '2024-07-19',
            update_time: '2024-07-19'
        },{
            level: '二级',
            id: 19221,
            pos: '1号楼栋',
            content: '人工上报',
            type: '安全',
            start_time: '2024-07-19',
            update_time: '2024-07-19'
        },{
            level: '二级',
            id: 19221,
            pos: '1号楼栋',
            content: '人工上报',
            type: '安全',
            start_time: '2024-07-19',
            update_time: '2024-07-19'
        },

    ],
    
});
</script>

<style lang="scss" scoped>
.sjlb {
    width: 801px;
    height: 562px;
}
</style>
<style lang="scss">
.sjlb {
    .el-table {
        width: 752px;
        margin-top: 30px;
        --el-table-border-color: rgba(255,255,255,0.2);
        background: none;
        color: #eee;
        font-size: 15px;
        tr {
            background: rgba(43, 144, 211, 0.2);
            height: 56px;
            &:nth-child(2n) {
                background: rgba(0,0,0,0);
            }
        }
        td.el-table__cell,th.el-table__cell.is-leaf {
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        &.el-table--border .el-table__cell {
            border-right: 1px solid rgba(255,255,255,0.2);
        }
        th.el-table__cell {
            background: rgba(43, 144, 211, 0.5);
            color: #fff;
        }
        &.el-table--fit {

        }
        .cell {
            padding: 0;
            text-align: center;
            white-space: nowrap;
        }
    }
    .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell{
        background-color: rgba(43, 144, 211, 0.2);
    }
}

</style>