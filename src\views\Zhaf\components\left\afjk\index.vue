<!--
 * @Description: 安防监控组件 - 南门监控视频画面
-->
<template>
    <Panel size="big" class="afjk" title="南门监控">
     
    </Panel>
</template>

<script setup>
import { reactive, onMounted, onUnmounted, watch } from "vue";
import { getWeather } from '@/apis';
import { useStore } from "vuex";

const data = reactive({
    currentTime: '',
    cameraStatus: 'online'
});

const store = useStore();
let timeInterval = null;

// 更新时间显示
const updateTime = () => {
    const now = new Date();
    data.currentTime = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
};

watch(
    () => store.state.updateTime.active,
    (newVal) => {
        getApi();
    },
)

onMounted(() => {
    updateTime();
    timeInterval = setInterval(updateTime, 1000);
    getApi();
})

onUnmounted(() => {
    if (timeInterval) {
        clearInterval(timeInterval);
    }
})

const getApi = async () => {
    try {
        const res = await getWeather();
        // 更新监控数据
    } catch (error) {
        console.log('API调用失败:', error);
    }
}
</script>

<style lang="scss" scoped>
.afjk {
    width: 490px;
    height: 285px;
    
    .monitor-container {
        padding: 10px;
        height: 100%;
        
        .video-screen {
            width: 100%;
            height: 100%;
            background: #000;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
            border: 2px solid #333;
            
            .video-content {
                width: 100%;
                height: 100%;
                position: relative;
                
                .monitor-bg {
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(135deg, 
                        rgba(20, 40, 60, 0.9) 0%, 
                        rgba(10, 25, 40, 0.9) 50%, 
                        rgba(5, 15, 25, 0.9) 100%),
                        url('@/assets/1 (1).png') center/cover;
                    position: relative;
                    
                    &::before {
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background: repeating-linear-gradient(
                            0deg,
                            transparent,
                            transparent 2px,
                            rgba(255, 255, 255, 0.03) 2px,
                            rgba(255, 255, 255, 0.03) 4px
                        );
                        pointer-events: none;
                    }
                }
                
                .monitor-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    pointer-events: none;
                    
                    .camera-info {
                        position: absolute;
                        top: 15px;
                        left: 15px;
                        display: flex;
                        gap: 15px;
                        
                        .camera-id {
                            background: rgba(0, 0, 0, 0.7);
                            color: #fff;
                            padding: 4px 8px;
                            border-radius: 4px;
                            font-size: 12px;
                            font-family: monospace;
                        }
                        
                        .recording-status {
                            background: rgba(255, 0, 0, 0.8);
                            color: #fff;
                            padding: 4px 8px;
                            border-radius: 4px;
                            font-size: 12px;
                            font-family: monospace;
                            animation: blink 1.5s infinite;
                        }
                    }
                    
                    .timestamp {
                        position: absolute;
                        bottom: 15px;
                        right: 15px;
                        background: rgba(0, 0, 0, 0.7);
                        color: #00ff00;
                        padding: 4px 8px;
                        border-radius: 4px;
                        font-size: 12px;
                        font-family: monospace;
                    }
                }
            }
        }
    }
}

@keyframes blink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0.5;
    }
}
</style> 