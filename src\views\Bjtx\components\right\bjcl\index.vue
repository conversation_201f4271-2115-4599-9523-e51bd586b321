
<template>
 
    <Panel size="big" class="bjcl" title="车辆信息统计">
        <div class="statistics-container">
            <!-- 授权总车辆统计条 -->
            <div class="total-bar">
                <img src="@/assets/Totalvehicle.png" alt="授权总车辆" class="icon" />
                <span class="text">授权总车辆</span>
                <span class="value">{{ vehicleData.totalVehicles }}</span>
            </div>
            
            <!-- 分类统计卡片组 -->
            <div class="category-cards">
                <!-- 员工授权车辆 -->
                <div class="category-card">
                    
                   
                        <div class="hexagon">
                            <img src="@/assets/Employeevehicles.png" alt="员工车辆" />
                        </div>
                   
                    <div class="card-info">
                        <div class="label">员工授权车辆</div>
                        <div class="number">{{ vehicleData.employeeVehicles }}</div>
                    </div>
                </div>
                
                <!-- 访客授权车辆 -->
                <div class="category-card">
                  
                       
                        <div class="hexagon">
                            <img src="@/assets/Visitorvehicle.png" alt="访客车辆" />
                        </div>
                    
                    <div class="card-info">
                        <div class="label">访客授权车辆</div>
                        <div class="number">{{ vehicleData.visitorVehicles }}</div>
                    </div>
                </div>
            </div>
        </div>
    </Panel>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import { getWeather } from '@/apis';
import { useStore } from "vuex";

// 车辆信息统计数据
const vehicleData = ref({
    totalVehicles: 1735,
    employeeVehicles: 1700,
    visitorVehicles: 35
});

const store = useStore();

watch(
    () => store.state.updateTime.active,
    (newVal) => {
        getApi();
    },
)

onMounted(() => {
    getApi();
})

const getApi = async () => {
    try {
        const res = await getWeather();
        // 后续接口出来后直接替换
        // vehicleData.value = res.vehicleStatistics || vehicleData.value;
    } catch (error) {
        console.log('API调用失败:', error);
    }
}
</script>

<style lang="scss" scoped>

.bjcl {
    width: 100%;
    max-width: 490px;
    height: 285px;
    
    /* 统计容器样式 */
    .statistics-container {
        padding: 20px;
        height: calc(100%);
        display: flex;
        flex-direction: column;
        // gap: 25px;
        
        /* 授权总车辆统计条 */
        .total-bar {
            width: 100%;
            height: 50px;
            background: linear-gradient( 180deg, rgba(33,39,53,0) 0%, rgba(18,197,210,0.3) 100%);
            border-radius: 0px 0px 10px 10px;
            border-radius: 25px;
            display: flex;
            align-items: center;
            padding: 0 25px;
            position: relative;
            
            .icon {
                width: 24px;
                height: 24px;
                margin-right: 12px;
                object-fit: contain;
            }
            
            .text {
                color: rgba(255, 255, 255, 0.8);
                font-size: 16px;
                flex: 1;
            }
            
            .value {
                color: #00D4FF;
                font-size: 26px;
              
             
            }
        }
        
        /* 分类统计卡片组 */
        .category-cards {
            display: flex;
            gap: 20px;
            flex: 1;
            
            .category-card {
                flex: 1;
                display: flex;
                align-items: center;
                gap: 20px;
                .hexagon{
                    width: 70px;
                    height: 70px;
                    // background: rgba(0, 212, 255, 0.1);
                    // border-radius: 10px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    img{
                        width: 100%;
                        height: 100%;
                        object-fit: contain;
                    }
                }

                
                /* 卡片信息 */
                .card-info {
                    flex: 1;
                    
                    .label {
                        
                        font-size: 16px;
                        margin-bottom: 8px;
                    }
                    
                    .number {
                        font-size: 26px;
                    }
                }
            }
        }
    }

}
</style> 