<!--
 * @Author: hkh <EMAIL>
 * @Date: 2024-07-17 17:03:41
 * @LastEditors: hkh <EMAIL>
 * @LastEditTime: 2024-07-29 17:16:15
 * @FilePath: \chengde-computer-room\src\views\zlan\components\left\sbzt\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="sbzt-box">
        <Panel size="medium" class="zbzt" title="设备状态">
            
            <div class="content">
                <div class="name">设备总数</div>
                <div class="value">{{data.total}}</div>
            </div>
            <div class="chart">
                <div class="legend">
                    <div>在线</div>
                    <div>离线</div>
                    <div>其他</div>
                </div>
                <HCharts :options="options" class="chart" />
                <!-- <img class="di" src="@/assets/zlan/di13.png" alt=""> -->
            </div>
        </Panel>
        <Panel size="medium" class="dlsyqk" title="电力使用情况">
            <div class="content">
                <div class="dl-left">100</div>
                <div class="dl-right">
                    <div class="name">总电力负载率</div>
                    <div class="value">33%</div>
                </div>
            </div>
            <div class="content">
                <div class="dl-left">99</div>
                <div class="dl-right">
                    <div class="name">IT负荷负载率</div>
                    <div class="value">35%</div>
                </div>
            </div>
        </Panel>
    </div>
</template>

<script setup>
import { reactive, computed, onMounted,watch } from "vue";
import Highcharts from 'highcharts'
import {getWeather} from '@/apis';
import { useStore } from "vuex";
const data = reactive({
    "total": 82, //总数
    "online": 17, //在线
    "offline": 7, //离线
    "other": 12, //其他
    "faultTotal": 1 //故障
});
const store = useStore();
watch(
    () => store.state.updateTime.active,
    (newVal) => {
        getApi();
    },
)
onMounted(()=>{
  getApi();
})
const getApi = async () => {
  const res = await getWeather();
//   data.total = res.total;
//   data.online = res.online;
//   data.offline = res.offline;
//   data.other = res.other;
//   data.faultTotal = res.faultTotal;
}
const options = computed(() => {
    return {
        update3d: true,
        chart: {
            type: "pie",
            options3d: {
                enabled: true,
                alpha: 70,
            },
            // margin:[-20,-20,-20,-20],
            spacing:[-10,-10,-10,-10],
            events: {
                load: function() {
                    const each = Highcharts.each;
                    const points = this.series[0].points;
                    each(points, function(p, i) {
                        p.graphic.attr({
                            translateY: -p.shapeArgs.ran,
                        });
                        p.graphic.side1.attr({
                            translateY: -p.shapeArgs.ran,
                        });
                        p.graphic.side2.attr({
                            translateY: -p.shapeArgs.ran,
                        });
                    });
                },
            },
        },
        title: {
            text: "",
            show: false
        },
        legend: {

        },
        tooltip: {
            enabled: false
        },
        plotOptions: {
            series: {
                allowPointSelect: false,
                cursor: 'pointer',
                innerSize: 70,
                // selected: true,
                dataLabels: {
                    enabled: false
                },
                // showInLegend: true,                
            },
        },
        series: [
            {
                name: "Medals",
                colorByPoint: true,
                colors: [
                    'rgb(255,255,255)','rgb(25,194,255)','rgb(34,225,215)',
                ],
                data: [
                    { name: "one", y: data.online, h: data.online, selected: true  },
                    { name: "four", y: data.offline, h: data.offline },
                    { name: "six", y: data.other, h: data.other },
                ],
            },
        ],
    };
});
</script>

<style lang="scss" scoped>
.sbzt-box {
    display: flex;
    justify-content: space-between;
}
.zbzt,
.dlsyqk {
    width: 325px;
    height: 360px;
}
.zbzt {
    .content {
        width: 100%;
        height: 41px;
        padding: 0 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 4px;
        margin-top: 10px;
        .name {
            flex: 1;
            margin: 0 20px;
        }
    }
}
.dlsyqk {
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    height: 100%;
    .content {
        width: 100%;
        height: 120px;
        padding: 0 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 4px;
        margin-top: 10px;
        .dl-left {
            width: 84px;
            height: 84px;
            // background: url("@/assets/zlan/quan1.png") no-repeat center center/100% 100%;
            color: #33dafa;
            font-size: 24px;
            text-align: center;
            line-height: 84px;
        }
        .dl-right {
            width: 150px;
            display: flex;
            justify-content: space-around;
            flex-direction: column;
            font-family: "PZDBTMF";
            .name {
                color: #fff;
                font-size: 20px;
            }
            .value {
                font-size: 32px;
                color: #45bcff;
                line-height: 32px;
                letter-spacing: 3px;
            }
        }
    }
}
.chart {
    height: 250px;
    position: relative;
    z-index: 1;
    .legend {
        // position: absolute;
        height: 80px;
        top: 10px;
        display: flex;
        justify-content: space-around;
        padding-left: 10px;
        align-items: center;
        width: 100%;
        &>div {
            position: relative;
            font-size: 20px;
            &::before {
                position: absolute;
                content: '';
                top: 9px;
                left: -16px;
                width: 8px;
                height: 8px;
                border-radius: 5px;
                background: rgba(255,255,255,0.5);
                border: 1px solid #fff;
            }
            &:nth-child(1) {
                &::before {
                    background: rgba(25,194,255,0.5);
                    border: 1px solid rgba(25,194,255,1);
                }
            }
            &:nth-child(2) {
                &::before {
                    background: rgba(34,225,215,0.5);
                    border: 1px solid rgba(34,225,215,1);
                }
            }
        }
    }
    .chart {
        height: 170px;
        margin: auto;
    }
    .di {
        position: absolute;
        width: 221px;
        height: 100px;
        bottom: 25px;
        left: 34px;
        z-index: -1;
    }
}
</style>