<template>
    <div>
        <Panel size="big" class="gzjd-container" title="整体面积分布">
            <div class="content-wrapper">
                <!-- 圆环图区域 -->
                <div class="chart-section">
                    <div class="pie-chart-container">
                        <Echarts :options="chartOptions" class="pie-chart" />
                        <div class="chart-center-text">
                            <div class="center-title">总面积</div>
                            <div class="center-value">{{ data.totalArea }}m²</div>
                            <div class="center-unit"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 统计数据区域 -->
                <div class="stats-section">
                    <div class="stat-item" v-for="(item, index) in data.areaList" :key="index">
                        <div class="stat-icon" :class="item.colorClass"></div>
                        <div class="stat-content">
                            <div class="stat-label">{{ item.name }} {{ item.area }}m²</div>
                            <div class="stat-percent">{{ item.percent }}%</div>
                        </div>
                    </div>
                </div>
            </div>
        </Panel>
    </div>
</template>

<script setup>
import { reactive, computed, onMounted, watch } from 'vue'
import { getWeather } from '@/apis'
import { useStore } from 'vuex'

// 导入图表组件
import Echarts from '@/components/Echarts'

const store = useStore()

// 数据定义
const data = reactive({
    totalArea: 53302,
    areaList: [
        { name: '办公区域', area: 15900, percent: 30, color: '#29CC82', colorClass: 'green' },
        { name: '会议室', area: 8500, percent: 16, color: '#3BA272', colorClass: 'blue' },
        { name: '展厅', area: 19402, percent: 36, color: '#73C0DE', colorClass: 'light-blue' },
        { name: '其他', area: 7500, percent: 14, color: '#D2653B', colorClass: 'orange' },
        { name: '宴会厅', area: 2000, percent: 4, color: '#FAC300', colorClass: 'yellow' }
    ]
})

// 图表配置
const chartOptions = computed(() => ({
    tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c}m² ({d}%)'
    },
    series: [{
        name: '面积分布',
        type: 'pie',
        radius: ['70%', '85%'],
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        label: {
            show: false
        },
        labelLine: {
            show: false
        },
        emphasis: {
            scale: false
        },
        data: data.areaList.map(item => ({
            value: item.area,
            name: item.name,
            itemStyle: {
                color: item.color
            }
        }))
    }]
}))

// 获取数据的API调用
const getApi = async () => {
    try {
        const res = await getWeather()
        console.log('面积分布数据更新:', res)
    } catch (error) {
        console.log('面积分布API调用失败:', error)
    }
}

// 监听数据更新
watch(
    () => store.state.updateTime?.active,
    (newVal) => {
        if (newVal) {
            getApi()
        }
    }
)

onMounted(() => {
    getApi()
})
</script>
 
<style scoped lang="scss">
.gzjd-container {
    width: 100%;
    height: 380px;
    
    .content-wrapper {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 100%;
        padding: 20px 10px;
        
        .chart-section {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            
            .pie-chart-container {
                width: 220px;
                height: 220px;
                position: relative;
                
                .pie-chart {
                    width: 100%;
                    height: 100%;
                }
                
                .chart-center-text {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    text-align: center;
                    pointer-events: none;
                    
                    .center-title {
                        color: rgba(255, 255, 255, 0.8);
                        font-size: 14px;
                        margin-bottom: 4px;
                        font-weight: 400;
                    }
                    
                    .center-value {
                        color: #ffffff;
                        font-size: 26px;
                        font-weight: bold;
                        font-family: 'Arial', sans-serif;
                        line-height: 1;
                        margin-bottom: 2px;
                    }
                    
                    .center-unit {
                        color: rgba(255, 255, 255, 0.6);
                        font-size: 12px;
                    }
                }
            }
        }
        
        .stats-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 12px;
            padding-left: 40px;
            
            .stat-item {
                display: flex;
                align-items: center;
                gap: 12px;
                
                .stat-icon {
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    flex-shrink: 0;
                    
                    &.green { 
                        background: #29CC82;
                        box-shadow: 0 0 8px rgba(41, 204, 130, 0.3);
                    }
                    &.blue { 
                        background: #3BA272;
                        box-shadow: 0 0 8px rgba(59, 162, 114, 0.3);
                    }
                    &.light-blue { 
                        background: #73C0DE;
                        box-shadow: 0 0 8px rgba(115, 192, 222, 0.3);
                    }
                    &.orange { 
                        background: #D2653B;
                        box-shadow: 0 0 8px rgba(210, 101, 59, 0.3);
                    }
                    &.yellow { 
                        background: #FAC300;
                        box-shadow: 0 0 8px rgba(250, 195, 0, 0.3);
                    }
                }
                
                .stat-content {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    flex: 1;
                    
                    .stat-label {
                        color: rgba(255, 255, 255, 0.9);
                        font-size: 13px;
                        font-weight: 400;
                        white-space: nowrap;
                    }
                    
                    .stat-percent {
                        // color: #00d4ff;
                        font-size: 14px;
                        // font-weight: bold;
                        // font-family: 'Arial', sans-serif;
                    }
                }
            }
        }
    }
}
</style>






