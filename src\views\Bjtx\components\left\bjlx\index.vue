<template>
    <!-- 使用Panel组件包裹 -->
    <Panel size="big" class="bjlx" title="人流量统计">
        <div class="flow-container">
            <!-- 图例 -->
            <div class="chart-legend">
                <div class="legend-item">
                    <span class="legend-dot" style="background: #00D4FF;"></span>
                    <span class="legend-text">人员进出数</span>
                </div>
                <div class="legend-item">
                    <span class="legend-dot" style="background: #7B68EE;"></span>
                    <span class="legend-text">人员出园数</span>
                </div>
            </div>
            
            <!-- 折线图 -->
            <div class="chart-wrapper">
                <Echarts :options="chartOptions" class="line-chart" />
            </div>
        </div>
    </Panel>
</template>

<script setup>
// 导入Panel组件和ECharts
import Panel from '@/components/Panel';
import Echarts from '@/components/Echarts';
import { ref, computed, onMounted, watch } from "vue";
import { getWeather } from '@/apis';
import { useStore } from "vuex";

// 人流量统计数据
const flowData = ref({
    timeLabels: ['00时', '03时', '06时', '09时', '12时', '15时', '18时', '21时'],
    inFlowData: [20, 40, 80, 120, 100, 80, 60, 30],
    outFlowData: [10, 20, 40, 60, 80, 100, 80, 40]
});

const store = useStore();

watch(
    () => store.state.updateTime.active,
    (newVal) => {
        getApi();
    },
)

onMounted(() => {
    getApi();
})

const getApi = async () => {
    try {
        const res = await getWeather();
        // 后续接口出来后直接替换
        // flowData.value = res.flowStatistics || flowData.value;
    } catch (error) {
        console.log('API调用失败:', error);
    }
}

// ECharts折线图配置
const chartOptions = computed(() => ({
    grid: {
        left: '10%',
        right: '5%',
        top: '15%',
        bottom: '15%'
    },
    xAxis: {
        type: 'category',
        data: flowData.value.timeLabels,
        axisLine: {
            lineStyle: {
                color: 'rgba(255, 255, 255, 0.2)'
            }
        },
        axisLabel: {
            color: 'rgba(255, 255, 255, 0.6)',
            fontSize: 12
        },
        splitLine: {
            show: false
        }
    },
    yAxis: {
        type: 'value',
        max: 150,
        splitNumber: 5,
        axisLine: {
            show: false
        },
        axisLabel: {
            color: 'rgba(255, 255, 255, 0.6)',
            fontSize: 12
        },
        splitLine: {
            lineStyle: {
                color: 'rgba(255, 255, 255, 0.1)'
            }
        }
    },
    series: [
        {
            name: '人员进出数',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            data: flowData.value.inFlowData,
            lineStyle: {
                color: '#00D4FF',
                width: 2
            },
            itemStyle: {
                color: '#00D4FF'
            },
            areaStyle: {
                color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                        { offset: 0, color: 'rgba(0, 212, 255, 0.3)' },
                        { offset: 1, color: 'rgba(0, 212, 255, 0.05)' }
                    ]
                }
            }
        },
        {
            name: '人员出园数',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            data: flowData.value.outFlowData,
            lineStyle: {
                color: '#7B68EE',
                width: 2
            },
            itemStyle: {
                color: '#7B68EE'
            },
            areaStyle: {
                color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                        { offset: 0, color: 'rgba(123, 104, 238, 0.3)' },
                        { offset: 1, color: 'rgba(123, 104, 238, 0.05)' }
                    ]
                }
            }
        }
    ],
    tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        textStyle: {
            color: '#fff'
        }
    }
}));
</script>

<style lang="scss" scoped>

.bjlx {
    width: 100%;  
    max-width: 490px;  
    height: 285px; 
    
    
    .flow-container {
        padding: 20px;
        height: 100%;
        display: flex;
        flex-direction: column;
        
       
        .chart-legend {
            display: flex;
            gap: 24px;
            margin-bottom: 20px;
            
            .legend-item {
                display: flex;
                align-items: center;
                gap: 8px;
                
                .legend-dot {
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                }
                
                .legend-text {
                    color: rgba(255, 255, 255, 0.7);
                    font-size: 13px;
                }
            }
        }
        
       
        .chart-wrapper {
            flex: 1;
            
            .line-chart {
                width: 100%;
                height: 100%;
            }
        }
    }
}
</style> 