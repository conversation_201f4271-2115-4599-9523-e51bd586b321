<template>
    <Panel size="big" class="tphj" title="本年产生电碳排放自用用电水量">
        <div class="environment-container">
            <div class="chart-section">
                <div class="usage-item electricity">
                    <div class="item-box">
                        <div class="icon-wrapper">
                            <img src="@/assets/dian.png" alt="用电量" class="usage-icon">
                        </div>
                        <div class="usage-info">
                            <div class="usage-label">同比</div>
                            <div class="usage-value">-{{ data.electricity }}%</div>
                        </div>
                    </div>
                    <div class="usage-detail">
                        <div class="detail-item">单台用电量: {{ data.electricityTotal }} kw.h</div>
                        <div class="detail-item">电量同比: {{ data.electricityCarbon }}%</div>
                    </div>
                </div>

                <div class="usage-item water">
                    <div class="item-box">
                        <div class="icon-wrapper">
                            <img src="@/assets/shui.png" alt="用水量" class="usage-icon">
                        </div>
                        <div class="usage-info">
                            <div class="usage-label">同比</div>
                            <div class="usage-value">-{{ data.water }}%</div>
                        </div>
                    </div>
                    <div class="usage-detail">
                        <div class="detail-item">单台用水量: {{ data.waterTotal }} 公斤</div>
                        <div class="detail-item">水量同比: {{ data.waterCarbon }}%</div>
                    </div>
                </div>
            </div>
        </div>
    </Panel>
</template>

<script setup>
import { reactive, onMounted, watch, computed } from "vue";
import { getWeather } from '@/apis';
import { useStore } from "vuex";

const data = reactive({
    electricity: 25,
    electricityTotal: 135,
    electricityCarbon: 25.00,
    water: 33,
    waterTotal: 17.5,
    waterCarbon: -33.00,
    totalCarbon: 15.8,
    carbonReduction: 12.5,
    efficiency: 1.25,
    energyLevel: 'A+'
});

const store = useStore();

// 计算圆环进度
const circumference = 2 * Math.PI * 50; // r = 50
const electricityCircumference = computed(() => circumference);
const waterCircumference = computed(() => circumference);
const electricityOffset = computed(() => circumference - (data.electricity / 100) * circumference);
const waterOffset = computed(() => circumference - (data.water / 100) * circumference);

watch(
    () => store.state.updateTime.active,
    (newVal) => {
        getApi();
    },
)

onMounted(() => {
    getApi();
})

const getApi = async () => {
    try {
        const res = await getWeather();
        // data.electricity = res.electricityUsage || 25;
        // data.water = res.waterUsage || 33;
    } catch (error) {
        console.log('API调用失败:', error);
    }
}
</script>

<style lang="scss" scoped>
.tphj {
    width: 490px;
    height: 285px;

    .environment-container {
        height: 100%;
        display: flex;
        flex-direction: column;

        .chart-section {
            display: flex;
            justify-content: space-between;
            align-items: stretch;
            flex: 1;
            gap: 20px;

            .usage-item {
                display: flex;
                align-items: start;
                flex-direction: column;
                gap: 15px;
                flex: 1;
                border-radius: 6px;
                padding: 25px 20px;
                min-height: 120px;

                .item-box {
                    display: flex;

                    .icon-wrapper {
                        margin-right: 20px;
                        .usage-icon {
                            width: 72px;
                            height: 72px;
                        }
                    }

                    .usage-info {
                        flex: 1;
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        gap: 6px;

                        .usage-label {
                            color: #ffffff;
                            font-size: 14px;
                            font-weight: 400;
                            opacity: 0.9;
                            line-height: 1.2;
                        }

                        .usage-value {
                            font-size: 28px;
                            font-weight: bold;
                            font-family: "PZDBTMF", Arial, sans-serif;
                            line-height: 1;
                            margin: 2px 0;
                            color: rgba(40, 200, 128, 1);
                        }

                        .usage-detail {
                            display: flex;
                            flex-direction: column;
                            gap: 2px;
                            margin-top: 4px;
                            align-items: flex-start;
                            .detail-item {
                                color: #ffffff;
                                font-size: 12px;
                                opacity: 0.75;
                                line-height: 1.3;
                            }
                        }
                    }

                    &.electricity {
                        .usage-value {
                            color: #FFD700;
                        }
                    }

                    &.water {
                        .usage-value {
                            color: #00BFFF;
                        }
                    }
                }
            }
        }

        .summary-section {
            display: flex;
            justify-content: space-around;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);

            .summary-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 15px 30px;
                background: rgba(0, 191, 255, 0.1);
                border-radius: 8px;
                border: 1px solid rgba(0, 191, 255, 0.3);

                .summary-title {
                    color: #fff;
                    font-size: 16px;
                    margin-bottom: 8px;
                    opacity: 0.8;
                }

                .summary-value {
                    color: #00bfff;
                    font-size: 28px;
                    font-weight: bold;
                    font-family: "PZDBTMF";
                    line-height: 1;
                    margin-bottom: 8px;
                }

                .summary-trend {
                    display: flex;
                    align-items: center;
                    gap: 8px;

                    .trend-label {
                        color: #fff;
                        font-size: 12px;
                        opacity: 0.6;
                    }

                    .trend-value {
                        font-size: 14px;
                        font-weight: bold;

                        &.decrease {
                            color: #4CAF50;
                        }

                        &.increase {
                            color: #ff4444;
                        }

                        &.level {
                            color: #FFD700;
                        }
                    }
                }
            }
        }
    }
}
</style>