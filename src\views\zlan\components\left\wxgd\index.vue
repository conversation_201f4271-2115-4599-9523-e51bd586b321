<!--
 * @Author: hkh <EMAIL>
 * @Date: 2024-07-18 11:02:25
 * @LastEditors: hkh <EMAIL>
 * @LastEditTime: 2024-07-23 14:13:17
 * @FilePath: \chengde-computer-room\src\views\zlan\components\left\wxgd\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <Panel size="big" class="wxgd" title="维修工单统计">
        <div class="content">
            <div class="left">
                <HCharts class="chart" pie3DNotnormalHight :options="options" />
                <!-- <img class="di" src="@/assets/zlan/di2.png" alt="" /> -->
            </div>
            <div class="right">
                <div>已处理 <span>120</span></div>
                <div>未处理 <span>90</span></div>
                <div>处理中 <span>100</span></div>
            </div>
        </div>
    </Panel>
</template>

<script setup>
import { reactive, computed } from "vue";
import Highcharts from "highcharts";
const options = computed(() => {
    return {
        update3d: true,
        chart: {
            type: "pie",
            options3d: {
                enabled: true,
                alpha: 75,
            },
            spacing: [20, 20, -73, 20],
            events: {
                load: function () {
                    const each = Highcharts.each;
                    const points = this.series[0].points;
                    each(points, function (p, i) {
                        p.graphic.attr({
                            translateY: -p.shapeArgs.ran,
                        });
                        p.graphic.side1.attr({
                            translateY: -p.shapeArgs.ran,
                        });
                        p.graphic.side2.attr({
                            translateY: -p.shapeArgs.ran,
                        });
                    });
                },
            },
        },
        title: {
            text: "",
            show: false,
        },
        legend: {},
        tooltip: {
            enabled: false,
        },
        plotOptions: {
            series: {
                allowPointSelect: false,
                cursor: "pointer",
                innerSize: 0,
                // selected: true,
                dataLabels: {
                    enabled: false,
                },
                // showInLegend: true,
            },
        },
        series: [
            {
                name: "Medals",
                colorByPoint: true,
                colors: [
                    {
                        linearGradient: {
                            x1: 0,
                            y1: 1,
                            x2: 1,
                            y2: 0,
                        },
                        // linearGradient:[0, 100, 100, 0],
                        // radialGradient: { cx: 0.5, cy: 0.3, r: 0.7 },
                        stops: [
                            [0, "rgba(255, 255, 255, 0.9)"],
                            // [0.1, "rgba(17, 177, 112, 0.5)"],
                            [1, "rgba(17, 177, 112, 1)"],
                            // [1, "rgba(255, 255, 255, 1)"],
                        ],
                    },
                    {
                        linearGradient: {
                            x1: 0,
                            y1: 1,
                            x2: 1,
                            y2: 0,
                        },
                        stops: [
                            [0, "rgba(236, 184, 48, 1)"],
                            [1, "rgba(236, 184, 48, 0.3)"],
                        ],
                    },
                    {
                        linearGradient: {
                            x1: 0,
                            y1: 1,
                            x2: 1,
                            y2: 0,
                        },
                        stops: [
                            [0, "rgba(1, 204, 251, 1)"],
                            [1, "rgba(1, 204, 251, 0.3)"],
                        ],
                    },
                    // 'rgba(17, 177, 112, 1)','rgba(236, 184, 48, 1)','rgba(1, 204, 251, 1)',
                ],
                data: [
                    { name: "one", y: 120, h: 40 },
                    { name: "four", y: 90, h: 15 },
                    { name: "six", y: 100, h: 28 },
                ],
            },
        ],
    };
});
</script>

<style lang="scss" scoped>
.wxgd {
    width: 660px;
    height: 366px;
    .content {
        height: 300px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .left {
            position: relative;
            width: 50%;
            height: 100%;
            .chart {
                height: 250px;
            }
            .di {
                position: absolute;
                left: 0;
                bottom: 10px;
                width: 305px;
                height: 126px;
                z-index: 0;
            }
        }
        .right {
            width: 50%;
            height: 100%;
            display: flex;
            justify-content: space-around;
            flex-direction: column;
            padding: 60px 0 40px 0;
            & > div {
                width: 100%;
                position: relative;
                font-size: 20px;
                padding-left: 30px;
                text-align: left;
                vertical-align: middle;
                span {
                    font-size: 40px;
                    margin-left: 10px;
                }
                &::before {
                    position: absolute;
                    content: "";
                    top: 29px;
                    left: -1px;
                    width: 8px;
                    height: 8px;
                    border-radius: 5px;
                    background: rgba(255, 255, 255, 0.5);
                    border: 1px solid #fff;
                }
                &:nth-child(1) {
                    &::before {
                        background: rgba(17, 177, 112, 0.5);
                        border: 1px solid rgba(17, 177, 112, 1);
                    }
                    span {
                        color: rgba(17, 177, 112, 1);
                    }
                }
                &:nth-child(2) {
                    &::before {
                        background: rgba(236, 184, 48, 0.5);
                        border: 1px solid rgba(236, 184, 48, 1);
                    }
                    span {
                        color: rgba(236, 184, 48, 1);
                    }
                }
                &:nth-child(3) {
                    &::before {
                        background: rgba(1, 204, 251, 0.5);
                        border: 1px solid rgba(1, 204, 251, 1);
                    }
                    span {
                        color: rgba(1, 204, 251, 1);
                    }
                }
            }
        }
    }
}
</style>