<template>
  <Panel size="big" class="zylyl" title="资源利用率">
    <div class="pan-content">
      <div class="pan-box">
        <div class="title">空间容量</div>
        <div class="content-box">
          <HCharts1 :options="options" class="chart" />
          <div class="content">
            <div class="content-item">
              <div class="name">总机柜数</div>
              <div class="value">{{data.dataList[0]}}台</div>
            </div>
            <div class="content-item">
              <div class="name">已占用</div>
              <div class="value">{{data.dataList[1]}}台</div>
            </div>
            <div class="content-item">
              <div class="name">剩余</div>
              <div class="value">{{data.dataList[2]}}台</div>
            </div>
          </div>
        </div>
      </div>
      <div class="pan-box">
        <div class="title">电力容量</div>
        <div class="content-box">
          <div class="chart">
            <!-- <img src="@/assets/zlan/dianchi.png" alt="" /> -->
          </div>
          <div class="content">
            <div class="content-item">
              <div class="name">总容量</div>
              <div class="value">10000kw</div>
            </div>
            <div class="content-item">
              <div class="name">已用容量</div>
              <div class="value">3300kw</div>
            </div>
            <div class="content-item">
              <div class="name">剩余</div>
              <div class="value">6700kw</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Panel>
</template>

<script setup>
import { reactive, computed, onMounted,watch } from "vue";
import Highcharts from "highcharts";
import {getWeather} from '@/apis';
import { useStore } from "vuex";
const data = reactive({
  dataList: [10,22,31]
});
const store = useStore();
watch(
    () => store.state.updateTime.active,
    (newVal) => {
        getApi();
    },
)
onMounted(()=>{
  getApi();
})
const getApi = async () => {
  const res = await getWeather();
  data.dataList = [res.totalCount,res.occupiedCount,res.freeCount]
}
const options = computed(() => {
  return {
    chart: {
      type: "pie",
      options3d: {
        enabled: true,
        alpha: 60,
      },
      // margin:[-20,-20,-20,-20],
      spacing: [-4, -4, -4, -4],
      // events: {
      //     load: function() {
      //         var points = this.series[0].points;
      //         points.forEach(function (p, i) {
      //             p.slicedTranslation = {
      //                 translateX: points[i + 1] ? (points[i + 1].plotX - p.plotX) / 2 : 0,
      //                 translateY: points[i + 1] ? (points[i + 1].plotY - p.plotY) / 2 : 0
      //             };
      //         });
      //     },
      // },
    },
    title: {
      text: "",
      show: false,
    },
    legend: {},
    tooltip: {
      enabled: false,
    },
    plotOptions: {
      series: {
        allowPointSelect: true,
        cursor: "pointer",
        innerSize: 0,
        depth: 35,
        // selected: true,
        dataLabels: {
          enabled: false,
        },
        // showInLegend: true,
      },
    },
    series: [
      {
        name: "Medals",
        colorByPoint: true,
        colors: [
          {
            linearGradient: {
              x1: 0,
              y1: 1,
              x2: 1,
              y2: 0,
            },
            stops: [
              [0, "rgba(52,214,127, 0.9)"],
              [1, "rgba(193,206,85, 0.9)"],
            ],
          },
          {
            linearGradient: {
              x1: 0,
              y1: 1,
              x2: 1,
              y2: 0,
            },
            stops: [
              [0, "rgba(13,115,228, 0.9)"],
              [1, "rgba(33,209,195, 0.9)"],
            ],
          },
          {
            linearGradient: {
              x1: 1,
              y1: 0,
              x2: 0,
              y2: 1,
            },
            stops: [
              [0, "rgba(217,92,65, 0.9)"],
              [1, "rgba(224,182,66, 0.9)"],
            ],
          },
        ],
        data: [
          { name: "one", y: data.dataList[2], h: data.dataList[2], sliced: true },
          { name: "four", y: data.dataList[1], h: data.dataList[1], sliced: true },
          { name: "six", y: data.dataList[0], h: data.dataList[0], sliced: true },
        ],
      },
    ],
  };
});
</script>

<style lang="scss" scoped>
.zylyl {
  width: 801px;
  height: 420px;
  .pan-content {
    display: flex;
    justify-content: space-between;
    padding: 20px 0;
    .pan-box {
      width: 360px;
      height: 310px;
      .title {
        width: 100%;
        border-bottom: 1px solid #fff;
        font-size: 20px;
      }
      .content-box {
        display: flex;
        justify-content: space-between;
        padding: 13px 0;
        .chart {
          width: 180px;
          height: 280px;
          display: flex;
          justify-content: center;
          align-items: center;
          img {
            width: 120px;
            height: 220px;
          }
        }
        .content {
          display: flex;
          justify-content: space-between;
          flex-direction: column;
          height: 290px;
          .content-item {
            width: 160px;
            height: 88px;
            padding: 10px 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            .name {
              width: 100%;
              font-size: 18px;
              color: #ffffff;
              border-bottom: 1px solid rgba(255, 255, 255, 0.4);
            }
            .value {
              font-size: 30px;
              white-space: nowrap;
            }
          }
        }
      }
    }
  }
}
</style>
