<template>
    <div>
        <Panel size="big" class="nhyh-container" title="环境告警统计">
            <div class="content-wrapper">
                <div class="chart-container">
                    <Echarts :options="chartOptions" class="bar-chart" />
                </div>
            </div>
        </Panel>
    </div>
</template>

<script setup>
import { reactive, computed, onMounted, watch } from 'vue'
import { trendsInWarn } from '@/apis'
import { useStore } from 'vuex'

// 导入图表组件
import Echarts from '@/components/Echarts'

const store = useStore()

// 数据定义
const data = reactive({
    dailyData: [
        { day: '周一', value: 45 },
        { day: '周二', value: 80 },
        { day: '周三', value: 90 },
        { day: '周四', value: 85 },
        { day: '周五', value: 75 },
        { day: '周六', value: 25 },
        { day: '周日', value: 55 }
    ]
})

// 图表配置
const chartOptions = computed(() => ({
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'shadow'
        }
    },
    grid: {
        left: '8%',
        right: '8%',
        bottom: '15%',
        top: '10%',
        containLabel: true
    },
    xAxis: {
        type: 'category',
        data: data.dailyData.map(item => item.day),
        axisLabel: {
            color: '#fff',
            fontSize: 12,
            margin: 15
        },
        axisLine: {
            show: false
        },
        axisTick: {
            show: false
        }
    },
    yAxis: {
        type: 'value',
        max: 100,
        interval: 20,
        axisLabel: {
            color: 'rgba(255, 255, 255, 0.6)',
            fontSize: 12
        },
        axisLine: {
            show: false
        },
        axisTick: {
            show: false
        },
        splitLine: {
            show: false
        }
    },
    series: [
        {
            type: 'bar',
            data: data.dailyData.map(item => item.value),
            itemStyle: {
                color: {
                    type: 'linear',
                    x: 0, y: 0, x2: 0, y2: 1,
                    colorStops: [
                        { offset: 0, color: '#80FDC2' },
                        { offset: 1, color: '#00A7FE' }
                    ]
                },
                borderRadius: [2, 2, 0, 0]
            },
            barWidth: 18,
            emphasis: {
                disabled: false
            }
        }
    ]
}))

const getEnvironmentAlarmStatistics = async () => {
    try {
        // 传递scene参数，值为environment（环境空间）
        const res = await trendsInWarn({ scene: 'environment' });
        console.log('环境告警统计数据:', res);
        
   
        if (res) {
      
        }
    } catch (error) {
        console.error('获取环境告警统计失败:', error);
    }
};

// 监听数据更新
watch(
    () => store.state.updateTime.active,
    (newVal) => {
        getEnvironmentAlarmStatistics()
    }
)

onMounted(() => {
    getEnvironmentAlarmStatistics()
})
</script>
 
<style scoped lang="scss">
.nhyh-container {
    width: 100%;
    height: 380px;
    
    .content-wrapper {
        height: 100%;
        padding: 20px 10px 10px 10px;
        
        .chart-container {
            height: 100%;
            
            .bar-chart {
                width: 100%;
                height: 100%;
            }
        }
    }
}
</style>






