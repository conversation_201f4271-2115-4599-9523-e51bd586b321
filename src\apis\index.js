/*
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2025-04-27 16:43:40
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-07-06 12:56:16
 * @FilePath: \blue-fat-ioc\src\apis\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { instanceClient } from "@/public/client";

// 获取token
export const getToken = (parmas) =>
    instanceClient.get("/token/generate?appid=client-app&secret=123456", { parmas });

// 获取天气信息
export const getWeather = (parmas) =>
    instanceClient.get("/weather", { parmas });

// 访客统计
export const dayVisitorStatistics = (params) => 
    instanceClient.get("/api/open/ioc/transport/v1/dayVisitorStatistics", { params });

window.dayVisitorStatistics=dayVisitorStatistics


//每日人流统计
export const dayPersonnelFlow = (params) => 
    instanceClient.get("/api/open/ioc/transport/v1/dayPersonnelFlow", { params });

// 环境空间-传感器数据（按监测点位查询环境监测数据指标）
export const monitorReal = (params) => 
    instanceClient.get("/api/open/ioc/environment/v1/monitorReal", { params });

// 高效办公-园区当日人流量统计
export const dayPersonnelStatistics = (params) => 
    instanceClient.get("/api/open/ioc/transport/v1/dayPersonnelStatistics", { params });


// 环境空间-环境告警统计(需传参scene)
export const trendsInWarn = (params) => 
    instanceClient.get("/api/open/ioc/warn/v1/trendsInWarn", { params });


// 环境空间-告警列表(需传参sceneId)          设备设施-告警明细(需传参scene)
export const warnList = (params) => 
    instanceClient.get("/api/open/ioc/warn/v1/warnList", { params });



// 环境空间-指标异常排名
export const topRanking = (params) => 
    instanceClient.get("/api/open/ioc/environment/v1/topRanking", { params });



// 环境空间-设备点位数据查询(交互接⼝)
export const deviceRealData = (params) => 
    instanceClient.get("/api/open/ioc/environment/v1/deviceRealData", { params });



// 设备设施-设备在离线统计
export const deviceOnlineData = (params) => 
    instanceClient.get("/api/open/ioc/building/v1/deviceOnlineData", { params });



// 设备设施-设备类型统计(已对接设备)
export const deviceTypeStatistics = (params) => 
    instanceClient.get("/api/open/ioc/building/v1/deviceTypeStatistics", { params });


// 设备设施-⼯单列表(需传参scene)
export const workOrderList = (params) => 
    instanceClient.get("/api/open/ioc/flow/v1/workOrderList", { params });


// 设备设施-本季度设备巡检清单(对应设备巡检状态列表)
export const deviceInspectionList = (params) => 
    instanceClient.get("/api/open/ioc/building/v1/deviceInspectionList", { params });
 

// 设备设施-告警明细(需传参scene)
// export const warnList= (params) => 
//     instanceClient.get("/api/open/ioc/warn/v1/warnList", { params });
