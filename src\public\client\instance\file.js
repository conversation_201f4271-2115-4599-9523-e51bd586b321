import axios from "axios";
import { AppGlobalConfig } from "@/public/global/const";

const instance = axios.create({
    baseURL: AppGlobalConfig.ServerUrl,
});

// axios.defaults.withCredentials = true;

instance.interceptors.request.use(async (config) => {
    config.responseType = "blob";
    return config;
});

instance.interceptors.response.use((response) => {
    const { data, headers } = response;
    const fileName = headers["content-disposition"].replace(/\w+;filename=(.*)/, "$1");
    const blob = new Blob([data], { type: headers["content-type"] });
    let dom = document.createElement("a");
    let url = window.URL.createObjectURL(blob);
    dom.href = url;
    dom.download = decodeURI(fileName);
    dom.style.display = "none";
    document.body.appendChild(dom);
    dom.click();
    dom.parentNode.removeChild(dom);
    window.URL.revokeObjectURL(url);
});

export default instance;
