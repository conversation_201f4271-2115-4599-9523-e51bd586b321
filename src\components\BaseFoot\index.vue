<!--
 * @Author: hkh <EMAIL>
 * @Date: 2024-07-16 17:38:35
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-05-18 17:57:32
 * @FilePath: \chengde-computer-room\src\components\BaseFoot\index.vue
 * @Description: 底部，包括menu
-->
<template>
    <div class="wt-foot">
        <div class="menu-box">
            <div
                class="menu click"
                :class="{ active: data.activemenu == item.route }"
                @click="gtMenu(item)"
                v-for="(item, index) in data.menuList"
                :key="index"
            >
                <img class="img" v-if="data.activemenu == item.route" :src="item.activeImg" alt="" />
                <img class="img" v-else :src="item.img" alt="" />
                <div class="name">{{ item.name }}</div>
                <div v-if="item.type === 'layer'" class="layer-box">
                    <div class="layer">摄像头</div>
                    <div class="layer">门禁</div>
                    <div class="layer">传感器</div>
                </div>
                <div v-if="item.type === 'build'" class="build-box">
                    <div class="build">第一栋</div>
                    <div class="build">第一栋</div>
                    <div class="build">第一栋</div>
                </div>
            </div>
        </div>
        <div class="foot-bg"></div>
    </div>
</template>

<script setup>
import { reactive, watch } from "vue";
import { useRouter } from "vue-router";
import ZhtsImg from "@/assets/zhts.png";
import ZhtsImgActive from "@/assets/zhts_check.png";
import ZhdtImg from "@/assets/zhdt.png";
// import ZhdtImgActive from "@/assets/底部综合安防选中***********";
import ZhafImg from "@/assets/zhaf.png";
import ZhafImgActive from "@/assets/zhaf_check.png";
import BjtxImg from "@/assets/bjtx.png";
import BjtxImgActive from "@/assets/bjtx_check.png";
import SbssImg from "@/assets/sbss.png";
import SbssImgActive from "@/assets/sbss_check.png";
import NhglImg from "@/assets/nhgl.png";
import NhglImgActive from "@/assets/nhgl_check.png";
import GxbgImg from "@/assets/gxbg.png";
import GxbgImgActive from "@/assets/gxbg_check.png";
import HjkjImg from "@/assets/hjkj.png";
import HjkjImgActive from "@/assets/hjkj_check.png";
import LayerImg from "@/assets/layer.png";
import BuildImg from "@/assets/build.png";

const data = reactive({
    menuList: [
        {
            img: LayerImg,
            type: 'layer'
        },
        {
            name: "综合态势",
            route: "zhts",
            img: ZhtsImg,
            activeImg: ZhtsImgActive,
        },
        {
            name: "智慧电梯",
            route: "zhdt",
            img: ZhdtImg,
            // activeImg: ZhdtImgActive,
            // activeImg: ZhtsImgActive,
        },
        {
            name: "综合安防",
            route: "zhaf",
            img: ZhafImg,
            activeImg: ZhafImgActive,
        },
        {
            name: "便捷通行",
            route: "bjtx",
            img: BjtxImg,
            activeImg: BjtxImgActive,
        },
        {
            name: "设备设施",
            route: "sbss",
            img: SbssImg,
            activeImg: SbssImgActive,
        },
        {
            name: "能耗管理",
            route: "nhgl",
            img: NhglImg,
            activeImg: NhglImgActive,
        },
        {
            name: "高效办公",
            route: "gxbg",
            img: GxbgImg,
            activeImg: GxbgImgActive,
        },
        {
            name: "环境空间",
            route: "hjkj",
            img: HjkjImg,
            activeImg: HjkjImgActive,
        },
        {
            img: BuildImg,
            type: 'build'
        }
    ],
    activemenu: null,
});
const router = useRouter();
watch(
    () => router.currentRoute.value,
    (newVal) => {
        console.log(newVal);
        data.activemenu = newVal.name;
    },
    // 可选项，是否开启深监听
    {
        deep: true,
        immediate: true,
    },
);
const gtMenu = (params) => {
    if(params.route) {
        router.push({
            name: params.route,
        });
    } 
    
};
</script>

<style lang="scss" scoped>
.wt-foot {
    position: absolute;
    z-index: 10;
    width: 55%;
    left: 50%;
    transform: translateX(-50%);
    bottom: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .foot-bg {
        margin: auto;
        width: 2239px;
        height: 36px;
        // background: url("@/assets/foot.png") no-repeat center center/100% 100%;
    }
    .menu-box {
        margin: auto;
        display: flex;
        flex-wrap: nowrap;
        width: 1283px;
        justify-content: space-between;
        align-items: flex-end;
        // margin-bottom: 20px;
        .menu {
            width: 235px;
            height: 59px;
            font-size: 12px;
            color: #fff;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            .img {
                width: 38px;
                height: 45px;
            }
            .name {
                font-family: "PZDBTMF";
            }
            .layer-box, .build-box {
                position: absolute;
                bottom: 70px;
                width: 55px;
                height: auto;
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 10px 0;
                background: rgba(0,0,0,0.3);
                border-radius: 4px;
            }
            &.active {
                line-height: 49px;
                // background: url("@/assets/menu-active-btn.png") no-repeat center center/100% 100%;
                color: #45bcff;
            }
            &:nth-child(1) {
                margin-bottom: -20px;
                img {
                    width: 53px;
                    height: 53px;
                }
            }
            &:nth-child(10) {
                margin-bottom: -20px;
                img {
                    width: 53px;
                    height: 53px;
                }
            }
            &:nth-child(2) {
                margin-bottom: 20px;
            }
            &:nth-child(3) {
                margin-bottom: 40px;
            }
            &:nth-child(4) {
                margin-bottom: 60px;
            }
            &:nth-child(5) {
                margin-bottom: 80px;
            }
            &:nth-child(6) {
                margin-bottom: 80px;
            }
            &:nth-child(7) {
                margin-bottom: 60px;
            }
            &:nth-child(8) {
                margin-bottom: 40px;
            }
            &:nth-child(9) {
                margin-bottom: 20px;
            }
            
        }
    }
}
</style>
