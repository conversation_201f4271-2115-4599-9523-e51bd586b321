/*
 * @Author: hkh <EMAIL>
 * @Date: 2024-07-16 16:13:16
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-04-29 08:43:19
 * @FilePath: \chengde-computer-room\src\main.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { createApp } from "vue";
import App from "./App.vue";
import { router } from "./router";
import store from "./store";
import { createPinia } from "pinia";
import "@/public/utils/helper.js";
import Adaption from "@/public/class/Adaption";
import Panel from '@/components/Panel';
import ECharts from "@/components/Echarts";
import HCharts from "@/components/HCharts";
import HCharts1 from "@/components/HCharts/index1.vue";
import "@/public/styles/index.scss";
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import "element-plus/theme-chalk/dark/css-vars.css";

import * as ElementPlusIconsVue from "@element-plus/icons-vue";
import zhCn from "element-plus/es/locale/lang/zh-cn";

// import Highcharts from 'highcharts/es-modules/Core/Chart/Chart.js';
// import LineSeries from 'highcharts/es-modules/Series/Line/LineSeries.js';
// import GaugeSeries from "highcharts/es-modules/Series/Gauge/GaugeSeries.js";

(window.adaption = new Adaption({
    height: 1290,
})).init();

// window.Highcharts = Highcharts;

import "uno.css";
import "@/public/styles";

import dayjs from "dayjs";
import "dayjs/locale/zh-cn";

dayjs.locale("zh-cn");


const app = createApp(App);
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component);
}
app.use(ElementPlus, {
    locale: zhCn,
});
app.use(router);
app.use(store);
app.use(createPinia());
app.mount("#app");
app.component('Panel', Panel)
app.component("ECharts", ECharts);
app.component("HCharts", HCharts);
app.component("HCharts1", HCharts1);

app.config.globalProperties.adaption = window.adaption;
