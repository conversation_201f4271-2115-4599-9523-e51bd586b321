<!--
 * @Author: 智慧园区开发团队
 * @Date: 2025-01-01
 * @Description: 大厅监控组件 - 只显示标题
-->
<template>
    <!-- 使用Panel组件包裹，设置尺寸和标题 -->
    <Panel size="big" class="afsp" title="大厅监控">
   
    </Panel>
</template>

<script setup>
// 导入Panel组件
import Panel from '@/components/Panel';
import { reactive, onMounted } from "vue";

// 组件数据
const data = reactive({
    // 后续可以添加监控相关数据
});

// 组件挂载时执行
onMounted(() => {
    // 初始化逻辑
});
</script>

<style lang="scss" scoped>
/* 大厅监控组件样式 */
.afsp {
    width: 490px;  /* 组件宽度 */
    height: 285px; /* 组件高度 */
    // background: red;

}
</style> 