<!--
 * @Author: hkh <EMAIL>
 * @Date: 2024-07-16 17:27:59
 * @LastEditors: hkh <EMAIL>
 * @LastEditTime: 2024-07-16 17:34:45
 * @FilePath: \chengde-computer-room\src\components\Layout\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="wt-layout">
        <slot>
            <router-view />
        </slot>
        <div class="wt-title"></div>
        <div class="wt-foot"></div>
    </div>
</template>

<script setup>

</script>

<style lang="scss" scoped>
.wt-layout {
    position: absolute;
    z-index: 1;
}
.wt-title {
    width: 100%;
    height: 148px;
    background: url("@/assets/boxTitleBg.png") no-repeat center center/100% 100%;;
}
.wt-foot {
    
}
</style>