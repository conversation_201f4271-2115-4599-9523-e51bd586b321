<template>
    <Panel size="big" class="kftj" title="来访客户">
        <div class="content-container">
            <div class="data-item">
                <div class="icon-container">
                    <div class="icon">月</div>
                </div>
                <div class="data-content">
                    <div class="title">本月访问</div>
                    <div class="number-container">
                        <span class="number">{{data.month}}</span>
                        <span class="unit">个</span>
                    </div>
                </div>
            </div>
            <div class="data-item">
                <div class="icon-container">
                    <div class="icon">年</div>
                </div>
                <div class="data-content">
                    <div class="title">今年访问量</div>
                    <div class="number-container">
                        <span class="number">{{data.year}}</span>
                        <span class="unit">个</span>
                    </div>
                </div>
            </div>
            <div class="data-item">
                <div class="icon-container">
                    <div class="icon">总</div>
                </div>
                <div class="data-content">
                    <div class="title">历史访问量</div>
                    <div class="number-container">
                        <span class="number">{{data.history}}</span>
                        <span class="unit">个</span>
                    </div>
                </div>
            </div>
        </div>
    </Panel>
</template>

<script setup>
import { reactive, onMounted, watch } from "vue";
import { getWeather } from '@/apis';
import { useStore } from "vuex";

const data = reactive({
    month: 356,
    year: 29841,
    history: 43224,
    rate: 0.62
});

const store = useStore();

watch(
    () => store.state.updateTime.active,
    (newVal) => {
        getApi();
    },
)

onMounted(() => {
    getApi();
})

const getApi = async () => {
    try {
        const res = await getWeather();
        // data.month = res.monthVisit || 356;
        // data.year = res.yearVisit || 29841;
        // data.history = res.historyVisit || 43224;
        // data.rate = res.growthRate || 0.62;
    } catch (error) {
        console.log('API调用失败:', error);
    }
}
</script>

<style lang="scss" scoped>
.kftj {
    width: 490px;
    height: 285px;
    
    .content-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20px;
        height: 100%;
        
        .data-item {
            display: flex;
            align-items: center;
            flex: 1;
            margin-right: 40px;
            flex-direction: column;
            &:last-child {
                margin-right: 20px;
            }
            
            .icon-container {
                width: 60px;
                height: 60px;
                margin-bottom: 20px;
                .icon {
                    width: 60px;
                    height: 60px;
                    background: linear-gradient(135deg, #1e90ff, #00bfff);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: #fff;
                    font-size: 24px;
                    font-weight: bold;
                    font-family: "PZDBTMF";
                }
            }
            
            .data-content {
                display: flex;
                flex-direction: column;
                align-items: center;

                .number-container {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                
                .title {
                    color: #fff;
                    font-size: 16px;
                    margin-bottom: 8px;
                    opacity: 0.8;
                }
                
                .number {
                    color: #fff;
                    font-size: 32px;
                    font-weight: bold;
                    font-family: "PZDBTMF";
                    line-height: 1;
                    margin-bottom: 4px;
                }
                
                .unit {
                    color: #fff;
                    font-size: 14px;
                    opacity: 0.6;
                    margin-left: 10px;
                }
            }
        }
        
        .rate-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, rgba(255, 0, 0, 0.2), rgba(255, 100, 100, 0.1));
            border-radius: 8px;
            padding: 15px 20px;
            border: 1px solid rgba(255, 0, 0, 0.3);
            
            .rate-text {
                display: flex;
                flex-direction: column;
                align-items: center;
                
                .rate-value {
                    color: #ff4444;
                    font-size: 20px;
                    font-weight: bold;
                    font-family: "PZDBTMF";
                    margin-bottom: 4px;
                }
                
                .rate-label {
                    color: #fff;
                    font-size: 12px;
                    opacity: 0.8;
                }
            }
        }
    }
}
</style> 