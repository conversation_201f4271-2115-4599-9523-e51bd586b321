<template>
    <div>
        <Panel size="big" class="sbjk-container" title="所有设备在线离线统计">
            <div class="content-wrapper">
                <!-- 圆环图区域 -->
                <div class="chart-section">
                    <div class="pie-chart-container">
                        <Echarts :options="chartOptions" class="pie-chart" />
                        <div class="chart-center-text">
                            <div class="center-title">设备状态</div>
       
                        </div>
                    </div>
                </div>
                
                <!-- 统计数据区域 -->
                <div class="stats-section">
                    <div class="stat-item">
                        <div class="stat-icon online-icon"></div>
                        <div class="stat-content">
                            <div class="stat-label">设备在线</div>
                            <div class="stat-value online">{{ data.onlineDevices }}</div>
                            <div class="stat-percent">{{ data.onlineRate }}%</div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon offline-icon"></div>
                        <div class="stat-content">
                            <div class="stat-label">设备离线</div>
                            <div class="stat-value offline">{{ data.offlineDevices }}</div>
                            <div class="stat-percent">{{ data.offlineRate }}%</div>
                        </div>
                    </div>
                </div>
            </div>
        </Panel>
    </div>
</template>

<script setup>
import { reactive, computed, onMounted, watch } from 'vue'
import { deviceOnlineData } from '@/apis'
import { useStore } from 'vuex'

// 导入图表组件
import Echarts from '@/components/Echarts'

const store = useStore()

// 数据定义
const data = reactive({
    totalDevices: 332,
    onlineDevices: 327,
    offlineDevices: 5,
    onlineRate: 98,
    offlineRate: 2
})

// 图表配置
const chartOptions = computed(() => ({
    tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [{
        name: '设备状态',
        type: 'pie',
        radius: ['70%', '85%'],
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        label: {
            show: false
        },
        labelLine: {
            show: false
        },
        emphasis: {
            scale: false
        },
        data: [
            {
                value: data.onlineDevices,
                name: '在线设备',
                itemStyle: {
                    color: '#00D478'
                }
            },
            {
                value: data.offlineDevices,
                name: '离线设备',
                itemStyle: {
                    color: '#FF4848'
                }
            }
        ]
    }]
}))

// 获取数据的API调用
const getDeviceOnlineData = async () => {
    const res = await deviceOnlineData();
    console.log(res);
};

// 监听数据更新
watch(
    () => store.state.updateTime?.active,
    (newVal) => {
        if (newVal) {
            getDeviceOnlineData()
        }
    }
)

onMounted(() => {
    getDeviceOnlineData()
})
</script>
 
<style scoped lang="scss">
.sbjk-container {
    width: 100%;
    height: 380px;
    
    .content-wrapper {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 100%;
        padding: 20px 10px;
        
        .chart-section {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            
            .pie-chart-container {
                width: 220px;
                height: 220px;
                position: relative;
                
                .pie-chart {
                    width: 100%;
                    height: 100%;
                }
                
                .chart-center-text {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    text-align: center;
                    pointer-events: none;
                    
                    .center-title {
                        color: rgba(255, 255, 255, 0.8);
                        font-size: 16px;
                        margin-bottom: 8px;
                        font-weight: 500;
                    }
                    
                    .center-value {
                        color: #00D4AA;
                        font-size: 32px;
                        font-weight: bold;
                        font-family: 'Arial', sans-serif;
                        line-height: 1;
                        margin-bottom: 4px;
                    }
                    
                    .center-unit {
                        color: rgba(255, 255, 255, 0.6);
                        font-size: 12px;
                    }
                }
            }
        }
        
        .stats-section {
            flex: 1;
            display: flex;
            flex-direction: column;
          
            gap: 30px;
            padding-left: 40px;
          
          
            .stat-item {
                display: flex;
                align-items: center;
                gap: 12px;
                padding: 12px 0;
                
                .stat-icon {
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    flex-shrink: 0;
                    
                    &.online-icon {
                        background: #00D478;
                        box-shadow: 0 0 8px rgba(0, 212, 120, 0.3);
                    }
                    
                    &.offline-icon {
                        background: #FF4848;
                        box-shadow: 0 0 8px rgba(255, 72, 72, 0.3);
                    }
                }
                
                .stat-content {
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    
                    .stat-label {
                        color: rgba(255, 255, 255, 0.9);
                        font-size: 12px;
                        font-weight: 400;
                        white-space: nowrap;
                        min-width: 50px;
                    }
                    
                    .stat-value {
                        color: #fff;
                        font-size: 16px;
                        font-weight: bold;
                        font-family: 'Arial', sans-serif;
                        line-height: 1;
                        min-width: 25px;
                        
                        &.online {
                            color: #00D478;
                        }
                        
                        &.offline {
                            color: #FF4848;
                        }
                    }
                    
                    .stat-percent {
                        color: rgba(255, 255, 255, 0.9);
                        font-size: 14px;
                        font-weight: 500;
                        font-family: 'Arial', sans-serif;
                        min-width: 35px;
                    }
                }
            }
        }
    }
}
</style>






