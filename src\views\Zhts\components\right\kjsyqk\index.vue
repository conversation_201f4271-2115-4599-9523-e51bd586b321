<template>
    <Panel size="big" class="kjsyqk" title="空间使用情况">
        <div class="space-usage-container">
            <div class="chart-section">
                <div class="pie-chart-container">
                    <Echarts :options="options" class="pie-chart" />
                    <div class="chart-center-text">
                        <div class="center-title">分布情况</div>
                    </div>
                </div>
                <div class="legend-section">
                    <div class="legend-item" v-for="(item, index) in data.spaceList" :key="index">
                        <div class="legend-dot" :style="{backgroundColor: item.color}"></div>
                        <div class="legend-content">
                            <span class="legend-name">{{item.name}} {{item.area}}m²</span>
                            <span class="legend-percent">{{item.percent}}%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </Panel>
</template>

<script setup>
import { reactive, onMounted, watch, computed } from "vue";
import { getWeather } from '@/apis';
import { useStore } from "vuex";

const data = reactive({
    spaceList: [
        { name: '办公区域', area: 15900, percent: 30, color: '#E91E63' },
        { name: '宴会厅', area: 8500, percent: 16, color: '#FF9800' },
        { name: '其他', area: 19402, percent: 36, color: '#3F51B5' },
        { name: '展厅', area: 7500, percent: 14, color: '#FF5722' },
        { name: '会议室', area: 2000, percent: 4, color: '#4CAF50' }
    ]
});

const store = useStore();

watch(
    () => store.state.updateTime.active,
    (newVal) => {
        getApi();
    },
)

onMounted(() => {
    getApi();
});

const getApi = async () => {
    try {
        const res = await getWeather();
        // data.spaceList = res.spaceUsage || data.spaceList;
    } catch (error) {
        console.log('API调用失败:', error);
    }
}

const options = computed(() => ({
    tooltip: {
        trigger: 'item',
        formatter: function(params) {
            const item = data.spaceList.find(space => space.name === params.name);
            return `${params.seriesName} <br/>${params.name}: ${params.value}% <br/>面积: ${item ? item.area : 0}m²`;
        },
        confine: true
    },
    series: [{
        name: '空间使用情况',
        type: 'pie',
        radius: ['70%', '85%'],
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        label: {
            show: false
        },
        labelLine: {
            show: false
        },
        itemStyle: {
            borderRadius: 0,
            borderColor: 'rgba(0,0,0,0)',
            borderWidth: 0
        },
        data: data.spaceList.map(item => ({
            value: item.percent,
            name: item.name,
            area: item.area,
            itemStyle: {
                color: item.color
            }
        }))
    }]
}));
</script>

<style lang="scss" scoped>
.kjsyqk {
    width: 100%;
    max-width: 490px;
    height: 310px;
    
    .space-usage-container {
        padding: 20px;
        height: 100%;
        display: flex;
        flex-direction: column;
        
        .chart-section {
            display: flex;
            align-items: center;
            gap: 30px;
            flex: 1;
            
            .pie-chart-container {
                width: 200px;
                height: 200px;
                position: relative;
                flex-shrink: 0;
                
                .pie-chart {
                    width: 100%;
                    height: 100%;
                }
                
                .chart-center-text {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    text-align: center;
                    pointer-events: none;
                    
                    .center-title {
                        color: #fff;
                        font-size: 16px;
                        font-weight: 500;
                        opacity: 0.9;
                    }
                }
            }
            
            .legend-section {
                flex: 1;
                display: flex;
                flex-direction: column;
                gap: 14px;
                padding-left: 20px;
                
                .legend-item {
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    
                    .legend-dot {
                        width: 8px;
                        height: 8px;
                        border-radius: 50%;
                        flex-shrink: 0;
                    }
                    
                    .legend-content {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        width: 100%;
                        
                        .legend-name {
                           width: 130px;
                            font-size: 14px;
                            
                            line-height: 1.2;
                        }
                        
                        .legend-percent {
                            color: #fff;
                            font-size: 16px;
                            // font-weight: bold;
                            // font-family: "PZDBTMF";
                            // line-height: 1;
                        }
                    }
                }
            }
        }
    }
}
</style> 