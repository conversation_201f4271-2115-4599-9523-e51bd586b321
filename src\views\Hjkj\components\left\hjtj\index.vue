<template>
    <div>
        <Panel size="big" class="hjtj-container" title="传感器数据">
            <div class="sensor-content">
                <div class="sensor-list">
                    <div class="sensor-item" v-for="(sensor, index) in sensorData" :key="index">
                        <div class="sensor-info">
                            <div class="sensor-name">{{ sensor.name }}</div>
                            <div class="sensor-details">
                                <div class="detail-row">
                                    <span class="detail-label">温度 :</span>
                                    <span class="detail-value">{{ sensor.temperature }}</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">湿度 :</span>
                                    <span class="detail-value">{{ sensor.humidity }}</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">PM2.5 :</span>
                                    <span class="detail-value">{{ sensor.pm25 }}</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">甲醛 :</span>
                                    <span class="detail-value">{{ sensor.formaldehyde }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </Panel>
    </div>
</template>

<script setup>
import { reactive, onMounted, watch } from 'vue'
import { monitorReal } from '@/apis'
import { useStore } from 'vuex'
const store = useStore()

// 传感器数据
const sensorData = reactive([
    {
        name: '传感器1',
        temperature: '25°C',
        humidity: '5%RH',
        pm25: '0.2μg/m³',
        formaldehyde: '0.2mg/m³'
    },
    {
        name: '传感器1',
        temperature: '25°C',
        humidity: '5%RH',
        pm25: '0.2μg/m³',
        formaldehyde: '0.2mg/m³'
    }
])

const getMonitorReal = async () => {
    const res = await monitorReal();
    console.log(res);
};

// 监听数据更新
watch(
    () => store.state.updateTime?.active,
    (newVal) => {
        if (newVal) {
            getMonitorReal()
        }
    }
)

onMounted(() => {
    getMonitorReal()
})
</script>
 
<style scoped lang="scss">
.hjtj-container {
    width: 100%;
    height: 380px;
    
    .sensor-content {
        height: calc(100% - 60px);
        padding: 20px;
        
        .sensor-list {
            display: flex;
            flex-direction: column;
            gap: 20px;
            
            .sensor-item {
                background: transparent;
                border: none;
                border-radius: 8px;
                padding: 15px;
                transition: all 0.3s ease;
                position: relative;
                
                &::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 50%;
                    background: linear-gradient(180deg, rgba(33,39,53,0) 0%, rgba(18,116,210,0.3) 100%);
                    border-radius: 0 0 8px 8px;
                    z-index: -1;
                }
                
  
                
                .sensor-info {
                    .sensor-name {
                        // color: #00d4ff;
                        font-size: 20px;
                        font-weight: bold;
                        margin-bottom: 10px;
                    }
                    
                    .sensor-details {
                        display: grid;
                        grid-template-columns: 1fr 1fr;
                        gap: 8px;
                        
                        .detail-row {
                            display: flex;
                            // justify-content: space-between;
                            align-items: center;
                            
                            .detail-label {
                                color: rgba(255, 255, 255, 0.8);
                                font-size: 14px;
                            }
                            
                            .detail-value {
                                color: #ffffff;
                                font-size: 14px;
                                font-weight: bold;
                                font-family: 'Arial', sans-serif;
                                margin-left: 15px;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>






