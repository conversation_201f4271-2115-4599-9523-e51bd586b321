<template>
    <Panel size="big" class="gjgd" title="告警工单统计">
        <ECharts :options="options" class="chart" />
    </Panel>
</template>

<script setup>
import { reactive, computed, onMounted,watch } from "vue";
import * as echarts from "echarts";
import {getWeather} from '@/apis';
import { useStore } from "vuex";
const data = reactive({
  dataList: [10,22,31]
});
const store = useStore();
watch(
    () => store.state.updateTime.active,
    (newVal) => {
        getApi();
    },
)
onMounted(()=>{
  getApi();
})
const getApi = async () => {
  const res = await getWeather();
  data.dataList = [res.processedCount,res.processingCount,res.unProcessedCount]
}
const options = computed(() => {
    const dataList = data.dataList;
    return {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none'
          },
          formatter: function(prams) {
            return prams[0].name + ':' + prams[0].data
          }
        },
        grid: {
          left: '8%',
          right: '2%',
          top: '15%',
          bottom: '15%'
        },
        xAxis: [
          {
            type: 'category',
            data: ['已处理','处理中','未处理'],
            axisTick: {
              show: false,
              alignWithLabel: true,
            },
            minorTick: {
              
            },
            // axisLine: {
            //   show: true,
            //   lineStyle: {
            //     type: 'dashed'
            //   }
            // },
            axisLabel: {
              // rotate: -18,
              color: '#fff',
              interval: 0,
              margin: 10,
              align: 'center',
              lineStyle: {
                type: 'dashed',
                color: '#00ff00'
              },
              fontWeight: 'bold',
              fontSize: window.adaption.fontSize(15),
            }
          }
        ],
        yAxis: {
          axisLine: {
            show: false
          },
          axisTick: {
            show: false,
            color: 'red',
            lineStyle: {
              type: 'dashed'
            }
          },
          splitLine: {
            color: '#fff',
            lineStyle: {
                type: 'dashed',
                color: 'rgba(255,255,255,0.3)'
              }
          },
          axisLabel: {
              color: 'rgba(255,255,255,0.7)',
              fontSize: window.adaption.fontSize(13),
            }
        },
        series: [
          //上面的圆圈和文字
          {
            name: '',
            type: 'pictorialBar',
            symbolSize: [30, 10],
            symbolOffset: [0, -5],
            symbolPosition: 'end',
            z: 12,
            label: {
              normal: {
                show: true,
                position: 'top',
                color: '#45BCFF',
                fontWeight: 'bold',
                fontSize: window.adaption.fontSize(13),
                // "formatter": "{c}%"
              }
            },
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 1,
                    color: '#8ED0FF',
                    opacity: 1
                  },
                  {
                    offset: 0,
                    color: '#1E8FE0',
                    opacity: 1
                  }
                ])
                // barBorderRadius: 11,
              }
            },
            data: dataList
          },
          //下面的圆圈
          {
            name: '',
            type: 'pictorialBar',
            symbolSize: [30, 10],
            symbolOffset: [0, 5],
            z: 12,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 1,
                    color: '#1E8FE0',
                    opacity: 1
                  },
                  {
                    offset: 0,
                    color: '#1E8FE0',
                    opacity: 1
                  }
                ])
              }
            },
            data: dataList
          },
          //最下面的圈
          {
            name: '',
            type: 'pictorialBar',
            symbolSize: [50, 20],
            symbolOffset: [0,12],
            z: 10,
            itemStyle: {
              normal: {
                  color: 'transparent',
                  borderColor: 'rgba(2, 163, 243, 1)', //底部外圆圈颜色
                  // borderType: 'dashed',
                  borderWidth: 4,
              },
            },
            data: dataList
        },
          //中间的柱子
          {
            type: 'bar',
            showBackground: false,
            // backgroundStyle: {
            //   color: 'rgba(216, 229, 247, 0.55)',
            //   borderRadius: [6, 6, 0, 0]
            // },
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: '#8ED0FF',
                    opacity: 0.85
                  },
                  {
                    offset: 1,
                    color: '#1E8FE0',
                    opacity: 0.79
                  }
                ])
              }
            },
            barWidth: '30',
            data: dataList
          }
        ]
    }
});
</script>

<style lang="scss" scoped>
.gjgd {
    width: 660px;
    height: 366px;
}
.chart {
    height: 280px;
}
</style>