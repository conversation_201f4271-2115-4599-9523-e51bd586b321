<!--
 * @Author: 智慧园区开发团队
 * @Date: 2025-05-18 14:38:23
 * @LastEditTime: 2025-05-18 17:40:00
 * @FilePath: \blue-fat-ioc\src\views\Zhaf\index.vue
 * @Description: 综合安防页面 - 包含左4右4共8个功能组件
-->
<template>
    <div>
      
        <LayoutLeft>
            <Afjk />    
            <Aftj />   
            <Afbj />    
            <Afgl />   
        </LayoutLeft>
        

        <LayoutRight>
            <Afsp />   
            <Afrz /> 
            <Afmj />    
            <Afcx />    
        </LayoutRight>
    </div>
</template>

<script setup>
// 导入布局组件
import LayoutLeft from '@/components/LayoutLeft';
import LayoutRight from '@/components/LayoutRight';


import Afjk from './components/left/afjk'      
import Aftj from './components/left/aftj'     
import Afbj from './components/left/afbj'      
import Afgl from './components/left/afgl'      


import Afsp from './components/right/afsp'   
import Afrz from './components/right/afrz' 
import Afmj from './components/right/afmj'    
import Afcx from './components/right/afcx'     
</script>

<style scoped>
</style>
