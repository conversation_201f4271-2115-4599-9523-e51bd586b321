<template>
    <div>
        <Panel size="big" class="gzbc-container" title="工位使用情况">
            <div class="content-wrapper">
                <!-- 圆环图区域 -->
                <div class="chart-section">
                    <div class="pie-chart-container">
                        <Echarts :options="chartOptions" class="pie-chart" />
                        <div class="chart-center-text">
                            <div class="center-title">工位总数</div>
                            <div class="center-number-row">
                                <span class="center-number">8315</span>
                                <span class="center-unit">个</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 统计数据区域 -->
                <div class="stats-section">
                    <div class="stat-item">
                        <div class="stat-icon" style="background: #83FFC1;"></div>
                        <div class="stat-content">
                            <div class="stat-label">固定工位</div>
                            <div class="stat-value" style="color: #83FFC1;">315个</div>
                            <div class="stat-percent">20%</div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon" style="background: #00A6FF;"></div>
                        <div class="stat-content">
                            <div class="stat-label">共享工位</div>
                            <div class="stat-value" style="color: #00A6FF;">8000个</div>
                            <div class="stat-percent">80%</div>
                        </div>
                    </div>
                </div>
            </div>
        </Panel>
    </div>
</template>

<script setup>
import { reactive, computed, onMounted, watch } from 'vue'
import { getWeather } from '@/apis'
import { useStore } from 'vuex'
const store = useStore()

// 数据定义
const data = reactive({
    totalWorkspace: 8315,
    fixedWorkspace: 315,  // 固定工位
    sharedWorkspace: 8000  // 共享工位
})

// 图表配置
const chartOptions = computed(() => ({
    tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c}个 ({d}%)'
    },
    series: [{
        name: '工位使用情况',
        type: 'pie',
        radius: ['70%', '85%'],
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        label: {
            show: false
        },
        labelLine: {
            show: false
        },
        emphasis: {
            scale: false
        },
        data: [
            {
                value: data.fixedWorkspace,
                name: '固定工位',
                itemStyle: {
                    color: '#83FFC1'
                }
            },
            {
                value: data.sharedWorkspace,
                name: '共享工位',
                itemStyle: {
                    color: '#00A6FF'
                }
            }
        ]
    }]
}))

// 获取数据的API调用
const getApi = async () => {
    try {
        const res = await getWeather()
        console.log('工位使用情况数据更新:', res)
    } catch (error) {
        console.log('工位使用情况API调用失败:', error)
    }
}

// 监听数据更新
watch(
    () => store.state.updateTime?.active,
    (newVal) => {
        if (newVal) {
            getApi()
        }
    }
)

onMounted(() => {
    getApi()
})
</script>
 
<style scoped lang="scss">
.gzbc-container {
    width: 100%;
    height: 380px;
    
    .content-wrapper {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 100%;
        padding: 20px 10px;
        
        .chart-section {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            
            .pie-chart-container {
                width: 220px;  /* 参考园区人流量组件的大小 */
                height: 220px; /* 参考园区人流量组件的大小 */
                position: relative;
                
                .pie-chart {
                    width: 100%;
                    height: 100%;
                }
                
                .chart-center-text {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    text-align: center;
                    pointer-events: none;
                    
                    .center-title {
                        // color: rgba(255, 255, 255, 0.7);
                        font-size: 16px;
                        font-weight: 400;
                        line-height: 1.2;
                        margin-bottom: 20px;
                    }
                    
                    .center-number-row {
                        display: flex;
                        align-items: baseline;
                        justify-content: center;
                        gap: 4px;
                    }
                    
                    .center-number {
                        // color: #ffffff;
                        font-size: 26px;
                        font-weight: bold;
                        line-height: 1;
                        font-family: 'Arial', sans-serif;
                    }
                    
                    .center-unit {
                        // color: rgba(255, 255, 255, 0.7);
                        font-size: 14px;
                        line-height: 1;
                    }
                }
            }
        }
        
        .stats-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 30px;  
            padding-left: 20px;
            
            .stat-item {
                display: flex;
                align-items: center;
                gap: 12px;
                
                .stat-icon {
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    flex-shrink: 0;
                }
                
                .stat-content {
                    display: flex;
                    align-items: center;
                    gap: 15px;
                    
                    .stat-label {
                        color: rgba(255, 255, 255, 0.9);
                        font-size: 14px;
                        font-weight: 400;
                        white-space: nowrap;
                    }
                    
                    .stat-value {
                        color: #ffffff;
                        font-size: 16px;
                        font-weight: bold;
                        font-family: 'Arial', sans-serif;
                        line-height: 1;
                    }
                    
                    .stat-percent {
                        color: rgba(255, 255, 255, 0.7);
                        font-size: 14px;
                    }
                }
            }
        }
    }
}
</style>






