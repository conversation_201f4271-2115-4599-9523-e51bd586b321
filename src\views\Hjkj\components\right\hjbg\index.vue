<!--
 * @Author: 智慧园区开发团队
 * @Date: 2025-01-01
 * @Description: 园区当日告警数据组件 - 美观的表格设计
-->
<template>
    <!-- 使用Panel组件包裹 -->
    <Panel size="big" class="afrz" title="园区当日告警数据">
        <div class="record-container">
        
            <div class="custom-table">
                <!-- 表头 -->
                <div class="table-header">
                    <div class="header-cell id-col">序号</div>
                    <div class="header-cell time-col">告警时间</div>
                    <div class="header-cell content-col">告警内容</div>
                    <div class="header-cell status-col">告警状态</div>
                </div>
                
                <!-- 表体 -->
                <div class="table-body">
                    <div 
                        v-for="(item, index) in alarmList" 
                        :key="index"
                        class="table-row"
                        :class="{ 'row-even': index % 2 === 0 }"
                    >
                        <div class="body-cell id-col">{{ item.id }}</div>
                        <div class="body-cell time-col" style="color: #fff;">{{ item.time }}</div>
                        <div class="body-cell content-col">{{ item.content }}</div>
                        <div class="body-cell status-col">
                            <span class="status-badge" :class="item.statusClass">
                                {{ item.status }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </Panel>
</template>

<script setup>
import { reactive, ref, onMounted, watch } from "vue";
import { warnList } from '@/apis';
import { useStore } from "vuex";

// 告警数据列表
const alarmList = ref([
    { id: 1, time: '2025-04-16 13:37:52', content: '3#5F厅行石柜', status: '告警', statusClass: 'status-alarm' },
    { id: 2, time: '2025-04-16 13:37:52', content: '3#5F厅行石柜', status: '正常', statusClass: 'status-normal' },
    { id: 3, time: '2025-04-16 13:53:22', content: '三期5#-1F消防设备', status: '正常', statusClass: 'status-normal' },
    { id: 4, time: '2025-04-16 13:53:22', content: '三期5#-1F消防设备', status: '告警', statusClass: 'status-alarm' },
    { id: 5, time: '2025-04-16 14:15:30', content: '北区电力监控', status: '正常', statusClass: 'status-normal' },
    { id: 6, time: '2025-04-16 14:25:45', content: '南区安防系统', status: '告警', statusClass: 'status-alarm' },
    { id: 7, time: '2025-04-16 14:40:12', content: '东门闸机设备', status: '正常', statusClass: 'status-normal' },
    { id: 8, time: '2025-04-16 15:10:33', content: '西区温度传感器', status: '告警', statusClass: 'status-alarm' }
]);

const store = useStore();

const getWarnList = async () => {
    try {
        // 传递scene参数，值为environment（环境空间）
        const res = await warnList({ scene: 'environment' });
        console.log('环境告警统计数据:', res);
        
   
        if (res) {
      
        }
    } catch (error) {
        console.error('获取环境告警统计失败:', error);
    }
};

 
watch(
    () => store.state.updateTime.active,
    (newVal) => {
        getWarnList()
    },
)

 
onMounted(() => {
    getWarnList()
})

const getApi = async () => {
    try {
        const res = await getWeather();
        // 替换
        // alarmList.value = res.alarmData || alarmList.value;
    } catch (error) {
        console.log('API调用失败:', error);
    }
}
</script>

<style lang="scss" scoped>

.afrz {
    width: 100%;
    // max-width: 490px;
    height: 380px;
    
    /* 记录容器样式 */
    .record-container {
        padding: 10px;
        height: calc(100%);
        overflow: hidden;
        
   
        .custom-table {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            
            
       
            .table-header {
                display: flex;
        
                
                .header-cell {
                    padding: 10px 4px;
                    // color: rgba(255, 255, 255, 0.9);
                    font-size: 14px;
                    text-align: center;
                    
                    &.id-col {
                        width: 18%;
                        min-width: 60px;
                    }
                    
                    &.time-col {
                        width: 35%;
                        min-width: 140px;
                        color: #fff;
                        // background: red;
                    }
                    
                    &.content-col {
                        width: 32%;
                        min-width: 120px;
                    }
                    
                    &.status-col {
                        width: 15%;
                        min-width: 60px;
                    }
                }
            }
            
            /* 表体样式 */
            .table-body {
                flex: 1;
                overflow-y: auto;
       
                
                /* 隐藏滚动条但保持滚动功能 */
                &::-webkit-scrollbar {
                    display: none;
                }
                scrollbar-width: none;
                -ms-overflow-style: none;
                
                .table-row {
                  display: flex;
                    &:last-child {
                        border-bottom: none;
                    }
                    .body-cell {
                        padding: 10px 10px;
                        color: rgba(255, 255, 255, 0.8);
                        font-size: 13px;
                        line-height: 1.4;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        text-align: center;
                        
                        &.id-col {
                            width: 18%;
                            min-width: 60px;
                            font-weight: 500;
                            color: rgba(255, 255, 255, 0.9);
                        }
                        
                        &.time-col {
                            width: 35%;
                            min-width: 140px;
                            color: rgba(255, 255, 255, 0.7);
                            font-family: 'Courier New', monospace;
                            white-space: nowrap; /* 时间在一行显示 */
                        }
                        
                        &.content-col {
                            width: 32%;
                            min-width: 120px;
                            overflow: hidden; /* 隐藏超出内容 */
                            text-overflow: ellipsis; /* 显示省略号 */
                            white-space: nowrap; /* 不换行 */
                            padding-left: 8px;
                            padding-right: 8px;
                        }
                        
                        &.status-col {
                            width: 15%;
                            min-width: 60px;
                        }
                    }
                }
            }
        }
        
        /* 状态徽章样式 */
        .status-badge {
            
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 12px;
            min-width: 50px;
            
            &.status-alarm {
            //   color: #B31B1B;
              background: #B31B1B;
            }
            
            &.status-normal {
                background: #1BB388;
            }
        }
    }
}
</style> 