<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2025-05-18 14:38:23
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-05-18 16:58:33
 * @FilePath: \blue-fat-ioc\src\views\Zhts\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div>
        <LayoutLeft>
            <Kftj />
            <Afjk />
            <Jrtx />
            <Tphj />
        </LayoutLeft>
        <LayoutRight>
            <Sbyxzt />
            <Hjjc />
            <Kjsyqk />
        </LayoutRight>
    </div>
</template>

<script setup>
import LayoutLeft from '@/components/LayoutLeft';
import LayoutRight from '@/components/LayoutRight';
import Kftj from './components/left/kftj'
import Afjk from './components/left/afjk'
import Jrtx from './components/left/jrtx'
import Tphj from './components/left/tphj'
import Sbyxzt from './components/right/sbyxzt'
import Hjjc from './components/right/hjjc'
import Kjsyqk from './components/right/kjsyqk'
</script>

<style scoped></style>
