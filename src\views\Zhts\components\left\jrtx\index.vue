<template>
    <Panel size="big" class="jrtx" title="今日通行">
        <div class="traffic-container">
            <div class="chart-section">
                <div class="chart-item">
                    <div class="chart-wrapper">
                        <Echarts :options="personOptions" class="ring-chart" />
                        <div class="chart-center">
                            <div class="total-label">人流量总数</div>
                            <div class="total-number">{{data.personTotal}}</div>
                        </div>
                    </div>
                    <div class="chart-legend">
                        <div class="legend-item">
                            <div class="legend-color" style="background: #00FF7F"></div>
                            <span class="legend-text">进入: {{data.personIn}}</span>
                            <span class="legend-percent">{{data.personInPercent}}%</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #1E90FF"></div>
                            <span class="legend-text">离开: {{data.personOut}}</span>
                            <span class="legend-percent">{{data.personOutPercent}}%</span>
                        </div>
                    </div>
                </div>
                
                <div class="chart-item">
                    <div class="chart-wrapper">
                        <Echarts :options="vehicleOptions" class="ring-chart" />
                        <div class="chart-center">
                            <div class="total-label">车流量总数</div>
                            <div class="total-number">{{data.vehicleTotal}}</div>
                        </div>
                    </div>
                    <div class="chart-legend">
                        <div class="legend-item">
                            <div class="legend-color" style="background: #FFD700"></div>
                            <span class="legend-text">进入: {{data.vehicleIn}}</span>
                            <span class="legend-percent">{{data.vehicleInPercent}}%</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #FF6347"></div>
                            <span class="legend-text">离开: {{data.vehicleOut}}</span>
                            <span class="legend-percent">{{data.vehicleOutPercent}}%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </Panel>
</template>

<script setup>
import { reactive, onMounted, watch, computed } from "vue";
import { getWeather } from '@/apis';
import { useStore } from "vuex";

const data = reactive({
    personIn: 954,
    personOut: 656,
    vehicleIn: 59,
    vehicleOut: 57
});

const store = useStore();

const personTotal = computed(() => data.personIn + data.personOut);
const vehicleTotal = computed(() => data.vehicleIn + data.vehicleOut);
const personInPercent = computed(() => personTotal.value > 0 ? Math.round((data.personIn / personTotal.value) * 100) : 0);
const personOutPercent = computed(() => personTotal.value > 0 ? Math.round((data.personOut / personTotal.value) * 100) : 0);
const vehicleInPercent = computed(() => vehicleTotal.value > 0 ? Math.round((data.vehicleIn / vehicleTotal.value) * 100) : 0);
const vehicleOutPercent = computed(() => vehicleTotal.value > 0 ? Math.round((data.vehicleOut / vehicleTotal.value) * 100) : 0);

// 将计算属性的值设置到 data 中以便在模板中使用
data.personTotal = personTotal;
data.vehicleTotal = vehicleTotal;
data.personInPercent = personInPercent;
data.personOutPercent = personOutPercent;
data.vehicleInPercent = vehicleInPercent;
data.vehicleOutPercent = vehicleOutPercent;

// 人员进出统计图表配置
const personOptions = computed(() => ({
    tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [{
        name: '人员进出',
        type: 'pie',
        radius: ['70%', '85%'],
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        label: {
            show: false
        },
        labelLine: {
            show: false
        },
        itemStyle: {
            borderRadius: 10, // 设置圆角大小
            borderColor: 'rgba(0,0,0,0)',
            borderWidth: 2
        },
        data: [
            {
                value: data.personIn,
                name: '进入',
                itemStyle: {
                    color: 'rgba(51, 105, 255, 1)'
                }
            },
            {
                value: data.personOut,
                name: '离开',
                itemStyle: {
                    color: 'rgba(255, 255, 255, 0.14)'
                }
            }
        ]
    }]
}));

// 车辆进出统计图表配置
const vehicleOptions = computed(() => ({
    tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [{
        name: '车辆进出',
        type: 'pie',
        radius: ['70%', '85%'],
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        label: {
            show: false
        },
        labelLine: {
            show: false
        },
        itemStyle: {
            borderRadius: 10, // 设置圆角大小
            borderColor: 'rgba(0,0,0,0)',
            borderWidth: 2
        },
        data: [
            {
                value: data.vehicleIn,
                name: '进入',
                itemStyle: {
                    color: 'rgba(41, 204, 130, 1)'
                }
            },
            {
                value: data.vehicleOut,
                name: '离开',
                itemStyle: {
                    color: 'rgba(255, 255, 255, 0.14)'
                }
            }
            
        ]
    }]
}));

watch(
    () => store.state.updateTime.active,
    (newVal) => {
        getApi();
    },
)

onMounted(() => {
    getApi();
})

const getApi = async () => {
    try {
        const res = await getWeather();
        // data.personIn = res.personIn || 954;
        // data.personOut = res.personOut || 656;
        // data.vehicleIn = res.vehicleIn || 59;
        // data.vehicleOut = res.vehicleOut || 57;
    } catch (error) {
        console.log('API调用失败:', error);
    }
}
</script>

<style lang="scss" scoped>
.jrtx {
    width: 100%;
    max-width: 490px;
    height: 285px;
    
    .traffic-container {
        padding: 20px;
        height: 100%;
        
        .chart-section {
            display: flex;
            justify-content: space-between;
            height: 100%;
            gap: 20px;
            
            .chart-item {
                flex: 1;
                display: flex;
                flex-direction: column;
                align-items: center;
                
                .chart-wrapper {
                    position: relative;
                    width: 160px;
                    height: 160px;
                    margin-bottom: 20px;
                    
                    .ring-chart {
                        width: 100%;
                        height: 100%;
                    }
                    
                    .chart-center {
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        text-align: center;
                        
                        .total-number {
                            color: #fff;
                            font-size: 22px;
                            font-family: "PZDBTMF";
                            line-height: 1;
                            margin-bottom: 4px;
                        }
                        
                        .total-label {
                            color: #fff;
                            font-size: 13px;
                            opacity: 0.8;
                        }
                    }
                }
                
                .chart-legend {
                    display: flex;
                    flex-direction: column;
                    gap: 8px;
                    width: 100%;
                    
                    .legend-item {
                        display: flex;
                        align-items: center;
                        gap: 8px;
                        padding: 8px 12px;
                        background: rgba(255, 255, 255, 0.05);
                        border-radius: 6px;
                        
                        .legend-color {
                            width: 12px;
                            height: 12px;
                            border-radius: 50%;
                        }
                        
                        .legend-text {
                            color: #fff;
                            font-size: 12px;
                            flex: 1;
                        }
                        
                        .legend-percent {
                            color: #00bfff;
                            font-size: 12px;
                            font-weight: bold;
                            font-family: "PZDBTMF";
                        }
                    }
                }
            }
        }
    }
    
    /* 响应式设计 */
    @media (max-width: 768px) {
        .traffic-container {
            padding: 15px;
            
            .chart-section {
                gap: 15px;
                
                .chart-item {
                    .chart-wrapper {
                        width: 120px;
                        height: 120px;
                        margin-bottom: 15px;
                        
                        .chart-center {
                            .total-number {
                                font-size: 18px;
                            }
                            
                            .total-label {
                                font-size: 11px;
                            }
                        }
                    }
                    
                    .chart-legend {
                        gap: 6px;
                        
                        .legend-item {
                            padding: 6px 8px;
                            
                            .legend-color {
                                width: 10px;
                                height: 10px;
                            }
                            
                            .legend-text {
                                font-size: 10px;
                            }
                            
                            .legend-percent {
                                font-size: 10px;
                            }
                        }
                    }
                }
            }
        }
    }
    
    @media (max-width: 480px) {
        .traffic-container {
            padding: 10px;
            
            .chart-section {
                flex-direction: column;
                gap: 20px;
                
                .chart-item {
                    .chart-wrapper {
                        width: 140px;
                        height: 140px;
                        margin-bottom: 15px;
                        
                        .chart-center {
                            .total-number {
                                font-size: 20px;
                            }
                            
                            .total-label {
                                font-size: 12px;
                            }
                        }
                    }
                    
                    .chart-legend {
                        gap: 8px;
                        
                        .legend-item {
                            padding: 8px 10px;
                            
                            .legend-color {
                                width: 12px;
                                height: 12px;
                            }
                            
                            .legend-text {
                                font-size: 11px;
                            }
                            
                            .legend-percent {
                                font-size: 11px;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style> 